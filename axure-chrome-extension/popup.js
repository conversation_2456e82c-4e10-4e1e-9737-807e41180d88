// Popup script for Axure RP Extension
document.addEventListener('DOMContentLoaded', function() {
  console.log('Popup loaded');
  
  // DOM elements
  const statusIndicator = document.getElementById('statusIndicator');
  const statusIcon = document.getElementById('statusIcon');
  const statusText = document.getElementById('statusText');
  const statusDetails = document.getElementById('statusDetails');
  const infoSection = document.getElementById('infoSection');
  const settingsSection = document.getElementById('settingsSection');
  const refreshBtn = document.getElementById('refreshBtn');
  const settingsBtn = document.getElementById('settingsBtn');
  const autoInjectCheck = document.getElementById('autoInjectCheck');
  const debugModeCheck = document.getElementById('debugModeCheck');
  const resetSettingsBtn = document.getElementById('resetSettingsBtn');
  
  // State
  let currentTab = null;
  let isSettingsVisible = false;
  
  // Initialize popup
  init();
  
  async function init() {
    try {
      // Get current tab
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      currentTab = tabs[0];
      
      // Load settings
      await loadSettings();
      
      // Check current page
      await checkCurrentPage();
      
      // Setup event listeners
      setupEventListeners();
      
    } catch (error) {
      console.error('Error initializing popup:', error);
      showError('初始化失败');
    }
  }
  
  async function loadSettings() {
    try {
      const settings = await chrome.storage.sync.get(['autoInject', 'enableDebug']);
      autoInjectCheck.checked = settings.autoInject !== false; // default true
      debugModeCheck.checked = settings.enableDebug === true; // default false
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  }
  
  async function saveSettings() {
    try {
      await chrome.storage.sync.set({
        autoInject: autoInjectCheck.checked,
        enableDebug: debugModeCheck.checked
      });
      console.log('Settings saved');
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  }
  
  async function checkCurrentPage() {
    if (!currentTab) {
      showStatus('error', '无法获取当前页面', '请刷新页面后重试');
      return;
    }
    
    showStatus('checking', '检测中...', '正在检测当前页面是否为Axure原型...');
    
    try {
      // Inject content script to check for Axure
      const results = await chrome.scripting.executeScript({
        target: { tabId: currentTab.id },
        function: detectAxureOnPage
      });
      
      if (results && results[0] && results[0].result) {
        const axureInfo = results[0].result;
        showAxureDetected(axureInfo);
      } else {
        showStatus('not-detected', '未检测到Axure原型', '当前页面不是Axure RP生成的原型页面');
      }
      
    } catch (error) {
      console.error('Error checking page:', error);
      showStatus('error', '检测失败', '无法检测当前页面，可能是权限问题');
    }
  }
  
  function detectAxureOnPage() {
    // This function runs in the page context
    const axureInfo = {
      isAxure: false,
      url: window.location.href,
      axureVersion: null,
      jqueryVersion: null,
      hasAxureLib: false,
      hasDataJs: false
    };
    
    // Check for Axure indicators
    const scripts = document.querySelectorAll('script[src]');
    for (let script of scripts) {
      const src = script.src.toLowerCase();
      if (src.includes('axure_rp_lib.js')) {
        axureInfo.hasAxureLib = true;
        axureInfo.isAxure = true;
      }
      if (src.includes('data.js')) {
        axureInfo.hasDataJs = true;
        axureInfo.isAxure = true;
      }
      if (src.includes('jquery')) {
        const match = src.match(/jquery[.-](\d+\.\d+\.\d+)/);
        if (match) {
          axureInfo.jqueryVersion = match[1];
        }
      }
    }
    
    // Check for Axure global objects
    if (window.$axure || window.axure || window.AxurePlayer) {
      axureInfo.isAxure = true;
    }
    
    // Check for Axure-specific DOM elements
    if (document.querySelector('#base') || 
        document.querySelector('.ax_default') ||
        document.querySelector('[data-label]')) {
      axureInfo.isAxure = true;
    }
    
    // Check meta generator
    const generator = document.querySelector('meta[name="generator"]');
    if (generator && generator.content.toLowerCase().includes('axure')) {
      axureInfo.isAxure = true;
      axureInfo.axureVersion = generator.content;
    }
    
    return axureInfo.isAxure ? axureInfo : null;
  }
  
  function showStatus(type, text, details) {
    // Remove existing status classes
    statusIndicator.className = 'status-indicator';
    
    // Add new status class
    statusIndicator.classList.add(`status-${type}`);
    
    // Update icon and text
    switch (type) {
      case 'detected':
        statusIcon.textContent = '✅';
        break;
      case 'not-detected':
        statusIcon.textContent = '❌';
        break;
      case 'checking':
        statusIcon.textContent = '🔄';
        break;
      case 'error':
        statusIcon.textContent = '⚠️';
        break;
      default:
        statusIcon.textContent = '⚪';
    }
    
    statusText.textContent = text;
    statusDetails.textContent = details;
    
    // Hide info section if not detected
    if (type !== 'detected') {
      infoSection.style.display = 'none';
    }
  }
  
  function showAxureDetected(axureInfo) {
    showStatus('detected', 'Axure原型已检测到', '扩展已激活，兼容性修复已应用');
    
    // Show info section
    infoSection.style.display = 'block';
    
    // Update info
    document.getElementById('pageUrl').textContent = axureInfo.url || '-';
    document.getElementById('axureVersion').textContent = axureInfo.axureVersion || '未知';
    document.getElementById('jqueryVersion').textContent = axureInfo.jqueryVersion || '未知';
  }
  
  function showError(message) {
    showStatus('error', '错误', message);
  }
  
  function setupEventListeners() {
    // Refresh button
    refreshBtn.addEventListener('click', async () => {
      await checkCurrentPage();
    });
    
    // Settings button
    settingsBtn.addEventListener('click', () => {
      toggleSettings();
    });
    
    // Settings checkboxes
    autoInjectCheck.addEventListener('change', saveSettings);
    debugModeCheck.addEventListener('change', saveSettings);
    
    // Reset settings button
    resetSettingsBtn.addEventListener('click', async () => {
      autoInjectCheck.checked = true;
      debugModeCheck.checked = false;
      await saveSettings();
      alert('设置已重置');
    });
    
    // Footer links
    document.getElementById('helpLink').addEventListener('click', (e) => {
      e.preventDefault();
      chrome.tabs.create({ url: 'https://github.com/your-repo/axure-chrome-extension#help' });
    });
    
    document.getElementById('feedbackLink').addEventListener('click', (e) => {
      e.preventDefault();
      chrome.tabs.create({ url: 'https://github.com/your-repo/axure-chrome-extension/issues' });
    });
    
    document.getElementById('aboutLink').addEventListener('click', (e) => {
      e.preventDefault();
      alert('Axure RP Extension v1.0.0\n\n现代Chrome浏览器的Axure原型预览扩展\n\n© 2024 Your Name');
    });
  }
  
  function toggleSettings() {
    isSettingsVisible = !isSettingsVisible;
    
    if (isSettingsVisible) {
      settingsSection.style.display = 'block';
      settingsBtn.textContent = '🔙 返回';
    } else {
      settingsSection.style.display = 'none';
      settingsBtn.innerHTML = '<span class="btn-icon">⚙️</span>设置';
    }
  }
});

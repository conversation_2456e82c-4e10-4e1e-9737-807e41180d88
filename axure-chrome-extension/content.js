// Content script for Axure RP Extension
console.log('Axure RP Extension content script loaded');

// Check if this page is an Axure prototype
function detectAxurePrototype() {
  // Look for Axure-specific elements and scripts
  const axureIndicators = [
    // Common Axure script files
    'jquery-1.7.1.min.js',
    'jquery-ui-1.8.10.custom.min.js',
    'axure_rp_lib.js',
    'data.js',
    'player.js',
    
    // Axure-specific DOM elements
    '#base',
    '#u0',
    '.ax_default',
    '[data-label]',
    
    // Axure-specific meta tags
    'meta[name="generator"][content*="Axure"]'
  ];
  
  // Check for script tags with Axure-related sources
  const scripts = document.querySelectorAll('script[src]');
  for (let script of scripts) {
    const src = script.src.toLowerCase();
    if (src.includes('axure') || src.includes('jquery-1.7.1') || src.includes('data.js')) {
      return true;
    }
  }
  
  // Check for Axure-specific DOM elements
  for (let selector of axureIndicators) {
    if (selector.startsWith('#') || selector.startsWith('.') || selector.startsWith('[')) {
      if (document.querySelector(selector)) {
        return true;
      }
    }
  }
  
  // Check for Axure generator meta tag
  const generator = document.querySelector('meta[name="generator"]');
  if (generator && generator.content.toLowerCase().includes('axure')) {
    return true;
  }
  
  // Check for Axure-specific global variables
  if (window.$axure || window.axure || window.AxurePlayer) {
    return true;
  }
  
  return false;
}

// Fix common Axure issues in modern browsers
function fixAxureCompatibility() {
  console.log('Applying Axure compatibility fixes...');
  
  // Fix for modern Chrome security restrictions
  if (window.location.protocol === 'file:') {
    // Enable local file access for Axure prototypes
    const originalXHR = window.XMLHttpRequest;
    window.XMLHttpRequest = function() {
      const xhr = new originalXHR();
      const originalOpen = xhr.open;
      
      xhr.open = function(method, url, async, user, password) {
        // Allow local file access
        if (url.startsWith('file://') || url.startsWith('./') || url.startsWith('../')) {
          // Apply CORS headers for local files
          xhr.addEventListener('readystatechange', function() {
            if (xhr.readyState === 4) {
              // Add necessary headers for Axure
              Object.defineProperty(xhr, 'responseText', {
                writable: true,
                value: xhr.responseText
              });
            }
          });
        }
        
        return originalOpen.apply(this, arguments);
      };
      
      return xhr;
    };
  }
  
  // Fix jQuery compatibility issues
  if (window.jQuery && window.jQuery.fn.jquery.startsWith('1.7')) {
    // Patch jQuery for modern browser compatibility
    const $ = window.jQuery;
    
    // Fix deprecated jQuery methods
    if (!$.fn.live) {
      $.fn.live = function(events, handler) {
        $(document).on(events, this.selector, handler);
        return this;
      };
    }
    
    if (!$.fn.die) {
      $.fn.die = function(events) {
        $(document).off(events, this.selector);
        return this;
      };
    }
  }
  
  // Fix CSS compatibility
  const style = document.createElement('style');
  style.textContent = `
    /* Fix for modern browser rendering */
    .ax_default {
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }
    
    /* Fix for flexbox issues */
    #base {
      display: block !important;
    }
    
    /* Fix for z-index issues */
    .ax_default {
      position: relative;
    }
  `;
  document.head.appendChild(style);
}

// Initialize when DOM is ready
function initialize() {
  if (detectAxurePrototype()) {
    console.log('Axure prototype detected!');
    
    // Notify background script
    chrome.runtime.sendMessage({
      action: 'axureDetected',
      url: window.location.href
    });
    
    // Apply compatibility fixes
    fixAxureCompatibility();
    
    // Add visual indicator
    addAxureIndicator();
  }
}

// Add visual indicator that the extension is active
function addAxureIndicator() {
  const indicator = document.createElement('div');
  indicator.id = 'axure-extension-indicator';
  indicator.innerHTML = '🔧 Axure RP Extension Active';
  indicator.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    background: #4CAF50;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-family: Arial, sans-serif;
    font-size: 12px;
    z-index: 999999;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    opacity: 0.9;
    transition: opacity 0.3s;
  `;
  
  // Auto-hide after 3 seconds
  setTimeout(() => {
    indicator.style.opacity = '0';
    setTimeout(() => {
      if (indicator.parentNode) {
        indicator.parentNode.removeChild(indicator);
      }
    }, 300);
  }, 3000);
  
  document.body.appendChild(indicator);
}

// Run initialization
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initialize);
} else {
  initialize();
}

// Also check after a delay for dynamically loaded content
setTimeout(initialize, 1000);

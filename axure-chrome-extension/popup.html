<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Axure RP Extension</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="container">
    <header class="header">
      <div class="logo">
        <img src="icons/icon32.png" alt="Axure RP" class="logo-icon">
        <h1>Axure RP Extension</h1>
      </div>
      <div class="version">v1.0.0</div>
    </header>

    <main class="main">
      <div class="status-section">
        <div class="status-indicator" id="statusIndicator">
          <div class="status-icon" id="statusIcon">⚪</div>
          <div class="status-text" id="statusText">检测中...</div>
        </div>
        <div class="status-details" id="statusDetails">
          正在检测当前页面是否为Axure原型...
        </div>
      </div>

      <div class="actions-section">
        <button class="action-btn primary" id="refreshBtn">
          <span class="btn-icon">🔄</span>
          重新检测
        </button>
        <button class="action-btn secondary" id="settingsBtn">
          <span class="btn-icon">⚙️</span>
          设置
        </button>
      </div>

      <div class="info-section" id="infoSection" style="display: none;">
        <h3>检测到的Axure信息</h3>
        <div class="info-item">
          <label>页面URL:</label>
          <span id="pageUrl">-</span>
        </div>
        <div class="info-item">
          <label>Axure版本:</label>
          <span id="axureVersion">-</span>
        </div>
        <div class="info-item">
          <label>jQuery版本:</label>
          <span id="jqueryVersion">-</span>
        </div>
      </div>

      <div class="settings-section" id="settingsSection" style="display: none;">
        <h3>扩展设置</h3>
        <div class="setting-item">
          <label class="checkbox-label">
            <input type="checkbox" id="autoInjectCheck">
            <span class="checkmark"></span>
            自动注入兼容性修复
          </label>
        </div>
        <div class="setting-item">
          <label class="checkbox-label">
            <input type="checkbox" id="debugModeCheck">
            <span class="checkmark"></span>
            启用调试模式
          </label>
        </div>
        <div class="setting-item">
          <button class="action-btn secondary" id="resetSettingsBtn">
            重置设置
          </button>
        </div>
      </div>
    </main>

    <footer class="footer">
      <div class="links">
        <a href="#" id="helpLink">帮助</a>
        <a href="#" id="feedbackLink">反馈</a>
        <a href="#" id="aboutLink">关于</a>
      </div>
    </footer>
  </div>

  <script src="popup.js"></script>
</body>
</html>

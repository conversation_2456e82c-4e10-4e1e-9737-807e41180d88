<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="generator" content="Axure RP Pro 9.0.0.3723">
    <title>Axure RP Extension 测试页面</title>
    
    <!-- 模拟Axure生成的标准头部 -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.7.1/jquery.min.js"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.8.10/jquery-ui.min.js"></script>
    
    <style>
        /* 模拟Axure样式 */
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        #base {
            position: relative;
            width: 1200px;
            height: 800px;
            margin: 20px auto;
            background: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .ax_default {
            position: absolute;
            cursor: pointer;
        }
        
        .ax_text {
            font-family: Arial, sans-serif;
        }
        
        .ax_button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .ax_button:hover {
            background: #45a049;
        }
        
        .ax_dynamic_panel {
            overflow: hidden;
            border: 1px solid #ddd;
        }
        
        .test-info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 10000;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <strong>Axure RP Extension 测试页面</strong><br>
        这是一个模拟的Axure原型页面，用于测试扩展功能
    </div>

    <div id="base">
        <!-- 模拟Axure生成的元素 -->
        <div class="ax_default ax_text" data-label="标题文本" style="left: 50px; top: 50px; width: 300px; height: 40px; font-size: 24px; font-weight: bold;">
            欢迎使用Axure RP Extension
        </div>
        
        <div class="ax_default ax_text" data-label="描述文本" style="left: 50px; top: 100px; width: 500px; height: 60px; font-size: 14px; line-height: 1.5;">
            这是一个测试页面，用于验证Chrome扩展是否能正确检测和处理Axure原型。
            如果您看到右上角显示绿色的"RP"徽章，说明扩展工作正常。
        </div>
        
        <div class="ax_default ax_button" data-label="测试按钮" style="left: 50px; top: 180px; width: 120px; height: 40px;" onclick="testClick()">
            点击测试
        </div>
        
        <div class="ax_default ax_dynamic_panel" data-label="动态面板" style="left: 200px; top: 180px; width: 300px; height: 200px; background: #f9f9f9;">
            <div style="padding: 20px;">
                <h3>动态面板测试</h3>
                <p>这是一个模拟的动态面板，用于测试扩展对Axure组件的兼容性。</p>
                <button onclick="togglePanel()" style="margin-top: 10px;">切换状态</button>
            </div>
        </div>
        
        <div class="ax_default" data-label="热区" style="left: 50px; top: 250px; width: 100px; height: 100px; background: rgba(255,0,0,0.2); border: 2px dashed red;" onclick="hotspotClick()">
            <div style="text-align: center; line-height: 96px; font-size: 12px;">热区测试</div>
        </div>
        
        <!-- 模拟表单元素 -->
        <div class="ax_default" style="left: 50px; top: 400px;">
            <label>输入框测试：</label>
            <input type="text" placeholder="请输入内容" style="margin-left: 10px; padding: 5px;">
        </div>
        
        <div class="ax_default" style="left: 50px; top: 440px;">
            <label>下拉框测试：</label>
            <select style="margin-left: 10px; padding: 5px;">
                <option>选项1</option>
                <option>选项2</option>
                <option>选项3</option>
            </select>
        </div>
        
        <!-- 状态显示区域 -->
        <div id="statusArea" style="position: absolute; left: 50px; top: 500px; width: 500px; height: 100px; background: #e8f5e8; border: 1px solid #4CAF50; padding: 10px; border-radius: 4px;">
            <h4>扩展状态检测</h4>
            <div id="extensionStatus">正在检测扩展状态...</div>
        </div>
    </div>

    <!-- 模拟Axure的数据文件 -->
    <script>
        // 模拟Axure的全局变量
        window.$axure = {
            utils: {
                getTransparentGifPath: function() {
                    return 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';
                }
            },
            player: {
                createAxureObject: function() {
                    return {};
                }
            }
        };
        
        // 测试函数
        function testClick() {
            alert('按钮点击测试成功！扩展正在正常工作。');
            updateStatus('按钮点击事件正常工作');
        }
        
        function hotspotClick() {
            alert('热区点击测试成功！');
            updateStatus('热区交互正常工作');
        }
        
        function togglePanel() {
            const panel = document.querySelector('.ax_dynamic_panel');
            if (panel.style.backgroundColor === 'lightblue') {
                panel.style.backgroundColor = '#f9f9f9';
                updateStatus('动态面板切换到状态1');
            } else {
                panel.style.backgroundColor = 'lightblue';
                updateStatus('动态面板切换到状态2');
            }
        }
        
        function updateStatus(message) {
            const statusDiv = document.getElementById('extensionStatus');
            const timestamp = new Date().toLocaleTimeString();
            statusDiv.innerHTML += `<br>[${timestamp}] ${message}`;
        }
        
        // 检测扩展是否加载
        function checkExtensionStatus() {
            setTimeout(function() {
                // 检查是否有扩展注入的元素
                const indicator = document.getElementById('axure-extension-indicator');
                if (indicator) {
                    updateStatus('✅ Axure RP Extension 已检测到并激活');
                } else {
                    updateStatus('⚠️ 未检测到扩展，请确保已正确安装');
                }
                
                // 检查jQuery版本
                if (window.jQuery) {
                    updateStatus(`jQuery版本: ${jQuery.fn.jquery}`);
                    
                    // 测试jQuery兼容性修复
                    if (jQuery.fn.live) {
                        updateStatus('✅ jQuery.fn.live 方法可用（兼容性修复成功）');
                    } else {
                        updateStatus('❌ jQuery.fn.live 方法不可用');
                    }
                }
                
                // 检查Axure对象
                if (window.$axure) {
                    updateStatus('✅ $axure 全局对象可用');
                }
                
            }, 2000);
        }
        
        // 页面加载完成后检测
        $(document).ready(function() {
            updateStatus('页面加载完成，开始检测扩展状态...');
            checkExtensionStatus();
            
            // 测试jQuery事件绑定
            $('.ax_default').hover(
                function() {
                    $(this).css('opacity', '0.8');
                },
                function() {
                    $(this).css('opacity', '1');
                }
            );
        });
        
        // 模拟Axure的页面加载事件
        window.onload = function() {
            console.log('Axure test page loaded');
            updateStatus('模拟Axure页面初始化完成');
        };
    </script>
</body>
</html>

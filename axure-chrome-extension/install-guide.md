# Axure RP Extension 安装指南

## 快速安装

### 步骤1：下载扩展文件
1. 下载整个 `axure-chrome-extension` 文件夹到您的电脑
2. 确保文件夹包含所有必要文件（manifest.json, background.js等）

### 步骤2：打开Chrome扩展管理页面
1. 打开Chrome浏览器
2. 在地址栏输入：`chrome://extensions/`
3. 或者通过菜单：更多工具 → 扩展程序

### 步骤3：启用开发者模式
1. 在扩展管理页面右上角找到"开发者模式"开关
2. 点击开关启用开发者模式

### 步骤4：加载扩展
1. 点击"加载已解压的扩展程序"按钮
2. 选择下载的 `axure-chrome-extension` 文件夹
3. 点击"选择文件夹"

### 步骤5：验证安装
1. 扩展安装成功后，会在扩展列表中显示"Axure RP Extension"
2. 浏览器工具栏会出现扩展图标
3. 访问任意Axure原型页面测试功能

## 详细安装说明

### 系统要求
- Chrome浏览器版本 88 或更高
- Windows 10/11, macOS 10.14+, 或 Linux

### 文件权限设置

#### Windows用户
1. 确保扩展文件夹有读取权限
2. 如果遇到权限问题，右键文件夹 → 属性 → 安全 → 编辑权限

#### macOS用户
1. 可能需要在系统偏好设置中允许Chrome访问文件
2. 系统偏好设置 → 安全性与隐私 → 隐私 → 文件和文件夹

#### Linux用户
1. 确保文件夹权限设置正确：`chmod -R 755 axure-chrome-extension`

### 启用文件URL访问
为了支持本地Axure文件（file://协议），需要额外设置：

1. 在Chrome扩展管理页面找到"Axure RP Extension"
2. 点击"详细信息"
3. 开启"允许访问文件网址"选项

### 故障排除

#### 问题1：无法加载扩展
**错误信息**：清单文件无效或缺失
**解决方案**：
1. 检查manifest.json文件是否存在且格式正确
2. 确保选择的是包含manifest.json的文件夹
3. 重新下载完整的扩展文件

#### 问题2：扩展加载后无法工作
**可能原因**：权限不足或文件损坏
**解决方案**：
1. 检查所有文件是否完整
2. 重新加载扩展：在扩展管理页面点击刷新按钮
3. 检查Chrome控制台是否有错误信息

#### 问题3：本地文件无法访问
**错误信息**：跨域请求被阻止
**解决方案**：
1. 确保已开启"允许访问文件网址"
2. 重启Chrome浏览器
3. 检查文件路径是否正确

## 高级安装选项

### 打包为CRX文件
如果您想创建可分发的安装包：

1. 在扩展管理页面点击"打包扩展程序"
2. 选择扩展文件夹路径
3. 点击"打包扩展程序"按钮
4. 生成的.crx文件可以分享给其他用户

### 企业部署
对于企业环境的批量部署：

1. 使用Chrome企业策略
2. 将扩展添加到强制安装列表
3. 通过组策略或MDM系统分发

## 验证安装成功

### 功能测试
1. 访问一个Axure原型页面
2. 点击扩展图标，应该显示检测状态
3. 如果检测到Axure原型，图标会显示绿色"RP"徽章

### 控制台检查
1. 按F12打开开发者工具
2. 在Console标签页查看是否有扩展相关的日志
3. 正常情况下应该看到"Axure RP Extension loaded"等信息

## 卸载扩展

如果需要卸载扩展：
1. 进入Chrome扩展管理页面
2. 找到"Axure RP Extension"
3. 点击"移除"按钮
4. 确认卸载

## 更新扩展

当有新版本时：
1. 下载新版本文件
2. 在扩展管理页面点击扩展的刷新按钮
3. 或者移除旧版本后重新安装新版本

## 技术支持

如果安装过程中遇到问题：
1. 检查Chrome版本是否符合要求
2. 查看本文档的故障排除部分
3. 在GitHub项目页面提交Issue
4. 提供详细的错误信息和系统环境

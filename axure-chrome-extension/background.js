// Background script for Axure RP Extension
console.log('Axure RP Extension background script loaded');

// Listen for tab updates to detect Axure prototypes
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    // Check if this might be an Axure prototype
    if (isAxurePrototype(tab.url)) {
      console.log('Potential Axure prototype detected:', tab.url);
      
      // Inject Axure support scripts
      chrome.scripting.executeScript({
        target: { tabId: tabId },
        files: ['axure-support.js']
      }).catch(err => {
        console.log('Could not inject script:', err);
      });
      
      // Inject Axure support styles
      chrome.scripting.insertCSS({
        target: { tabId: tabId },
        files: ['axure-styles.css']
      }).catch(err => {
        console.log('Could not inject CSS:', err);
      });
    }
  }
});

// Function to detect if a URL might be an Axure prototype
function isAxurePrototype(url) {
  // Check for common Axure file patterns
  const axurePatterns = [
    /\.html$/i,
    /index\.html$/i,
    /start\.html$/i,
    /home\.html$/i
  ];
  
  // Check for file:// protocol (local files)
  if (url.startsWith('file://')) {
    return axurePatterns.some(pattern => pattern.test(url));
  }
  
  // Check for common Axure hosting patterns
  const axureHosts = [
    /axshare\.com/i,
    /axure\.cloud/i,
    /localhost/i
  ];
  
  return axureHosts.some(pattern => pattern.test(url)) || 
         axurePatterns.some(pattern => pattern.test(url));
}

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
  // Open popup or perform action
  console.log('Extension icon clicked for tab:', tab.url);
});

// Listen for messages from content scripts
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'axureDetected') {
    console.log('Axure prototype confirmed on tab:', sender.tab.id);
    
    // Update extension badge or icon to indicate Axure is detected
    chrome.action.setBadgeText({
      tabId: sender.tab.id,
      text: 'RP'
    });
    
    chrome.action.setBadgeBackgroundColor({
      tabId: sender.tab.id,
      color: '#4CAF50'
    });
    
    sendResponse({success: true});
  }
  
  if (request.action === 'getSettings') {
    // Return extension settings
    chrome.storage.sync.get(['enableDebug', 'autoInject'], (result) => {
      sendResponse(result);
    });
    return true; // Keep message channel open for async response
  }
});

// Initialize extension
chrome.runtime.onInstalled.addListener(() => {
  console.log('Axure RP Extension installed');
  
  // Set default settings
  chrome.storage.sync.set({
    enableDebug: false,
    autoInject: true
  });
});

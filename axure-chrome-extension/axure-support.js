// Axure support script - injected into pages to provide compatibility
console.log('Axure support script loaded');

// Enhanced Axure compatibility fixes
(function() {
  'use strict';
  
  // Fix for file:// protocol restrictions
  if (window.location.protocol === 'file:') {
    // Override fetch for local file access
    const originalFetch = window.fetch;
    window.fetch = function(input, init) {
      const url = typeof input === 'string' ? input : input.url;
      
      if (url.startsWith('./') || url.startsWith('../') || url.includes('.js') || url.includes('.css')) {
        // For local files, try to use XMLHttpRequest as fallback
        return new Promise((resolve, reject) => {
          const xhr = new XMLHttpRequest();
          xhr.open('GET', url, true);
          xhr.onload = function() {
            if (xhr.status === 200 || xhr.status === 0) {
              resolve(new Response(xhr.responseText, {
                status: xhr.status,
                statusText: xhr.statusText,
                headers: new Headers({
                  'Content-Type': xhr.getResponseHeader('Content-Type') || 'text/plain'
                })
              }));
            } else {
              reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
            }
          };
          xhr.onerror = () => reject(new Error('Network error'));
          xhr.send();
        });
      }
      
      return originalFetch.apply(this, arguments);
    };
  }
  
  // Fix jQuery compatibility for older Axure versions
  function fixJQueryCompatibility() {
    if (window.jQuery) {
      const $ = window.jQuery;
      
      // Fix deprecated methods
      if (!$.fn.live && $.fn.on) {
        $.fn.live = function(events, handler) {
          $(document).on(events, this.selector, handler);
          return this;
        };
      }
      
      if (!$.fn.die && $.fn.off) {
        $.fn.die = function(events) {
          $(document).off(events, this.selector);
          return this;
        };
      }
      
      // Fix browser detection for modern browsers
      if ($.browser) {
        $.browser.chrome = /chrome/.test(navigator.userAgent.toLowerCase());
        $.browser.webkit = /webkit/.test(navigator.userAgent.toLowerCase());
        $.browser.version = navigator.userAgent.match(/Chrome\/(\d+)/)?.[1] || '100';
      }
    }
  }
  
  // Fix Axure player compatibility
  function fixAxurePlayer() {
    // Wait for Axure objects to be available
    const checkAxure = setInterval(() => {
      if (window.$axure || window.axure) {
        clearInterval(checkAxure);
        
        // Fix common Axure issues
        if (window.$axure) {
          // Override problematic methods
          const originalUtils = window.$axure.utils;
          if (originalUtils) {
            // Fix getTransparentGifPath for modern browsers
            if (originalUtils.getTransparentGifPath) {
              const originalGetTransparentGifPath = originalUtils.getTransparentGifPath;
              originalUtils.getTransparentGifPath = function() {
                try {
                  return originalGetTransparentGifPath.apply(this, arguments);
                } catch (e) {
                  // Fallback to data URL
                  return 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';
                }
              };
            }
          }
        }
        
        console.log('Axure player compatibility fixes applied');
      }
    }, 100);
    
    // Stop checking after 10 seconds
    setTimeout(() => clearInterval(checkAxure), 10000);
  }
  
  // Fix CSS and rendering issues
  function fixRenderingIssues() {
    const style = document.createElement('style');
    style.textContent = `
      /* Fix for modern browser rendering */
      .ax_default {
        box-sizing: border-box;
      }
      
      /* Fix for image rendering */
      img[src*="transparent.gif"] {
        opacity: 0;
      }
      
      /* Fix for text rendering */
      .ax_default p, .ax_default div {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      
      /* Fix for interaction states */
      .ax_default:hover {
        cursor: pointer;
      }
      
      /* Fix for popup panels */
      .ax_popup {
        z-index: 10000 !important;
      }
      
      /* Fix for dynamic panels */
      .ax_dynamic_panel {
        overflow: hidden;
      }
      
      /* Fix for repeaters */
      .ax_repeater {
        display: block;
      }
    `;
    
    if (document.head) {
      document.head.appendChild(style);
    } else {
      document.addEventListener('DOMContentLoaded', () => {
        document.head.appendChild(style);
      });
    }
  }
  
  // Apply all fixes
  function applyFixes() {
    fixJQueryCompatibility();
    fixAxurePlayer();
    fixRenderingIssues();
    
    console.log('All Axure compatibility fixes applied');
  }
  
  // Run fixes when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', applyFixes);
  } else {
    applyFixes();
  }
  
  // Also run fixes after a delay for dynamic content
  setTimeout(applyFixes, 500);
  setTimeout(applyFixes, 2000);
  
})();

# 更新日志

所有重要的项目变更都会记录在这个文件中。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [1.0.0] - 2024-08-22

### 新增
- 🎉 初始版本发布
- ✨ 自动检测Axure RP生成的HTML原型文件
- ✨ 智能注入兼容性修复脚本和样式
- ✨ 支持file://协议的本地原型文件
- ✨ 修复jQuery 1.7.x在现代浏览器中的兼容性问题
- ✨ 用户友好的popup界面，显示检测状态和扩展信息
- ✨ 可配置的设置选项（自动注入、调试模式）
- ✨ 视觉状态指示器，绿色"RP"徽章显示检测状态
- ✨ 支持多种Axure版本（8.x, 9.x, 10.x）

### 技术特性
- 🔧 基于Manifest V3的现代Chrome扩展架构
- 🔧 Content Script自动检测和修复机制
- 🔧 Web Accessible Resources注入支持脚本
- 🔧 Background Service Worker处理扩展逻辑
- 🔧 Chrome Storage API保存用户设置

### 修复的问题
- 🐛 修复"XMLHttpRequest cannot load file://"跨域错误
- 🐛 修复"jQuery.fn.live is not a function"兼容性错误
- 🐛 修复现代浏览器中的CSS渲染问题
- 🐛 修复Axure动态面板显示异常
- 🐛 修复热区和交互元素点击失效
- 🐛 修复透明图片显示问题

### 支持的功能
- ✅ Axure基本组件（文本、按钮、图片等）
- ✅ 动态面板和状态切换
- ✅ 热区和链接交互
- ✅ 表单元素（输入框、下拉框等）
- ✅ 弹出面板和对话框
- ✅ 中继器组件
- ✅ 流程图和连接器
- ✅ 注释和标记

### 文档
- 📚 完整的README.md使用说明
- 📚 详细的安装指南（install-guide.md）
- 📚 打包和部署指南（package-guide.md）
- 📚 测试页面和示例代码

## [计划中的功能]

### v1.1.0 - 增强功能
- 🔮 支持更多Axure组件类型
- 🔮 添加原型性能监控
- 🔮 支持自定义CSS注入
- 🔮 添加快捷键支持
- 🔮 改进错误报告机制

### v1.2.0 - 高级特性
- 🔮 支持Axure Cloud原型
- 🔮 添加原型截图功能
- 🔮 支持批量原型处理
- 🔮 集成开发者工具面板

### v2.0.0 - 重大更新
- 🔮 支持Axure RP 11+新特性
- 🔮 重构核心架构
- 🔮 添加团队协作功能
- 🔮 支持原型版本管理

## 已知问题

### v1.0.0
- ⚠️ 某些复杂的自定义JavaScript可能需要手动调整
- ⚠️ 非标准Axure模板可能需要额外配置
- ⚠️ 大型原型文件加载可能较慢

## 兼容性

### 支持的浏览器版本
- Chrome 88+
- Chromium 88+
- Microsoft Edge 88+

### 支持的Axure版本
- Axure RP 8.0+
- Axure RP 9.0+
- Axure RP 10.0+

### 支持的操作系统
- Windows 10/11
- macOS 10.14+
- Linux (Ubuntu 18.04+)

## 贡献者

- 主要开发者：[您的姓名]
- 测试人员：[测试团队]
- 文档编写：[文档团队]

## 致谢

感谢以下项目和资源的支持：
- Axure RP官方文档
- Chrome扩展开发社区
- jQuery兼容性解决方案
- 开源社区的反馈和建议

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目主页：https://github.com/your-username/axure-chrome-extension
- 问题反馈：https://github.com/your-username/axure-chrome-extension/issues
- 邮箱：<EMAIL>

---

**注意**：版本号遵循语义化版本规范：
- 主版本号：不兼容的API修改
- 次版本号：向下兼容的功能性新增
- 修订号：向下兼容的问题修正

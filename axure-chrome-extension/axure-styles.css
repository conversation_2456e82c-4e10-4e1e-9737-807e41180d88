/* Axure RP Extension - Compatibility Styles */

/* Fix for modern browser rendering */
.ax_default {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  box-sizing: border-box;
  position: relative;
}

/* Fix for base container */
#base {
  display: block !important;
  position: relative;
  overflow: visible;
}

/* Fix for Axure widgets */
.ax_default div,
.ax_default span,
.ax_default p {
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Fix for images */
.ax_default img {
  max-width: none;
  height: auto;
  border: none;
  outline: none;
}

/* Fix for transparent images */
img[src*="transparent.gif"],
img[src*="spacer.gif"] {
  opacity: 0;
  pointer-events: none;
}

/* Fix for text widgets */
.ax_default .ax_text {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Fix for button widgets */
.ax_default .ax_button {
  cursor: pointer;
  display: inline-block;
  text-decoration: none;
}

.ax_default .ax_button:hover {
  opacity: 0.8;
}

/* Fix for input widgets */
.ax_default input,
.ax_default textarea,
.ax_default select {
  box-sizing: border-box;
  font-family: inherit;
  font-size: inherit;
}

/* Fix for dynamic panels */
.ax_dynamic_panel {
  overflow: hidden;
  position: relative;
}

.ax_dynamic_panel_state {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Fix for popup panels */
.ax_popup {
  position: absolute;
  z-index: 10000 !important;
  background: white;
  border: 1px solid #ccc;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Fix for repeaters */
.ax_repeater {
  display: block;
  position: relative;
}

.ax_repeater_item {
  position: relative;
  display: block;
}

/* Fix for hotspots */
.ax_hotspot {
  cursor: pointer;
  position: absolute;
}

/* Fix for shapes */
.ax_shape {
  position: relative;
  display: inline-block;
}

/* Fix for inline frames */
.ax_inline_frame {
  border: none;
  overflow: hidden;
}

/* Fix for tree nodes */
.ax_tree_node {
  display: block;
  position: relative;
}

/* Fix for menu items */
.ax_menu_item {
  cursor: pointer;
  display: block;
  position: relative;
}

.ax_menu_item:hover {
  background-color: #f0f0f0;
}

/* Fix for flow shapes */
.ax_flow_shape {
  position: relative;
  display: inline-block;
}

/* Fix for connectors */
.ax_connector {
  position: absolute;
  pointer-events: none;
}

/* Fix for annotations */
.ax_annotation {
  position: absolute;
  background: #ffffcc;
  border: 1px solid #cccc00;
  padding: 4px;
  font-size: 11px;
  z-index: 1000;
}

/* Fix for selection rectangles */
.ax_selection {
  position: absolute;
  border: 2px dashed #0066cc;
  background: rgba(0, 102, 204, 0.1);
  pointer-events: none;
}

/* Fix for drag and drop */
.ax_drag_item {
  position: absolute;
  z-index: 9999;
  opacity: 0.8;
}

/* Fix for loading indicators */
.ax_loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.9);
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Fix for error messages */
.ax_error {
  color: #cc0000;
  background: #ffeeee;
  border: 1px solid #cc0000;
  padding: 8px;
  margin: 8px;
  border-radius: 4px;
}

/* Fix for responsive behavior */
@media screen and (max-width: 768px) {
  #base {
    min-width: auto !important;
    width: 100% !important;
  }
  
  .ax_default {
    max-width: 100%;
  }
}

/* Fix for print styles */
@media print {
  .ax_popup,
  .ax_annotation,
  .ax_selection {
    display: none !important;
  }
  
  #base {
    position: static !important;
  }
}

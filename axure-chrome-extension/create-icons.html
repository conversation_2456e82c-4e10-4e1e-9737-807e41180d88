<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Extension Icons</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .icon-container {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        .icon-item {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        .icon-item h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        canvas {
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            margin: 10px 5px;
            padding: 8px 16px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #5a6fd8;
        }
        .instructions {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="instructions">
        <h2>Axure RP Extension 图标生成器</h2>
        <p>这个页面可以帮您生成Chrome扩展所需的各种尺寸图标。点击下载按钮保存图标到icons文件夹。</p>
    </div>

    <div class="icon-container">
        <div class="icon-item">
            <h3>16x16</h3>
            <canvas id="icon16" width="16" height="16"></canvas>
            <br>
            <button onclick="downloadIcon('icon16', 'icon16.png')">下载</button>
        </div>
        
        <div class="icon-item">
            <h3>32x32</h3>
            <canvas id="icon32" width="32" height="32"></canvas>
            <br>
            <button onclick="downloadIcon('icon32', 'icon32.png')">下载</button>
        </div>
        
        <div class="icon-item">
            <h3>48x48</h3>
            <canvas id="icon48" width="48" height="48"></canvas>
            <br>
            <button onclick="downloadIcon('icon48', 'icon48.png')">下载</button>
        </div>
        
        <div class="icon-item">
            <h3>128x128</h3>
            <canvas id="icon128" width="128" height="128"></canvas>
            <br>
            <button onclick="downloadIcon('icon128', 'icon128.png')">下载</button>
        </div>
    </div>

    <div class="instructions">
        <h3>使用说明：</h3>
        <ol>
            <li>点击各个尺寸图标下方的"下载"按钮</li>
            <li>将下载的图标文件保存到 <code>axure-chrome-extension/icons/</code> 文件夹中</li>
            <li>确保文件名与manifest.json中指定的名称一致</li>
        </ol>
    </div>

    <script>
        // 绘制Axure RP风格的图标
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const scale = size / 48; // 基于48px设计，然后缩放
            
            // 清除画布
            ctx.clearRect(0, 0, size, size);
            
            // 背景渐变
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // 绘制圆角矩形背景
            const radius = 6 * scale;
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.roundRect(2 * scale, 2 * scale, size - 4 * scale, size - 4 * scale, radius);
            ctx.fill();
            
            // 绘制"A"字母
            ctx.fillStyle = 'white';
            ctx.font = `bold ${Math.floor(28 * scale)}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('A', size / 2, size / 2);
            
            // 添加小的"RP"标识
            if (size >= 32) {
                ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                ctx.font = `${Math.floor(8 * scale)}px Arial`;
                ctx.textAlign = 'right';
                ctx.textBaseline = 'bottom';
                ctx.fillText('RP', size - 4 * scale, size - 4 * scale);
            }
        }
        
        // 为Canvas添加圆角矩形方法（如果不支持）
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
        
        // 下载图标
        function downloadIcon(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        // 初始化所有图标
        function initIcons() {
            drawIcon(document.getElementById('icon16'), 16);
            drawIcon(document.getElementById('icon32'), 32);
            drawIcon(document.getElementById('icon48'), 48);
            drawIcon(document.getElementById('icon128'), 128);
        }
        
        // 页面加载完成后初始化图标
        window.addEventListener('load', initIcons);
    </script>
</body>
</html>

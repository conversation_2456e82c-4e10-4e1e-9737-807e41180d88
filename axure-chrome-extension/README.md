# Axure RP Extension for Chrome

现代Chrome浏览器的Axure RP原型预览扩展，替代已不再支持的旧版插件。

## 功能特性

- ✅ **自动检测** - 智能识别Axure RP生成的HTML原型文件
- ✅ **兼容性修复** - 解决现代Chrome浏览器的兼容性问题
- ✅ **本地文件支持** - 支持file://协议的本地原型文件
- ✅ **jQuery修复** - 修复旧版jQuery在现代浏览器中的问题
- ✅ **样式优化** - 优化原型在现代浏览器中的显示效果
- ✅ **用户友好** - 直观的状态指示和设置界面

## 安装方法

### 方法一：开发者模式安装（推荐）

1. 下载或克隆此项目到本地
2. 打开Chrome浏览器，进入 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目文件夹 `axure-chrome-extension`
6. 扩展安装完成

### 方法二：打包安装

1. 在扩展管理页面点击"打包扩展程序"
2. 选择项目文件夹，生成.crx文件
3. 将.crx文件拖拽到Chrome扩展页面安装

## 使用说明

1. **自动检测**：访问Axure原型页面时，扩展会自动检测并应用兼容性修复
2. **手动检测**：点击扩展图标，在弹窗中点击"重新检测"
3. **查看状态**：扩展图标会显示绿色徽章"RP"表示检测到Axure原型
4. **设置选项**：在弹窗中可以调整自动注入和调试模式等设置

## 支持的Axure版本

- Axure RP 8.x
- Axure RP 9.x
- Axure RP 10.x
- 其他使用jQuery 1.7.x的旧版本

## 解决的问题

### 现代Chrome浏览器兼容性问题
- 修复file://协议的跨域限制
- 解决jQuery 1.7.x的兼容性问题
- 修复CSS渲染问题
- 解决JavaScript API变更问题

### 常见错误修复
- "XMLHttpRequest cannot load file://" 错误
- jQuery.fn.live is not a function 错误
- 样式显示异常
- 交互功能失效

## 项目结构

```
axure-chrome-extension/
├── manifest.json          # 扩展清单文件
├── background.js          # 后台脚本
├── content.js            # 内容脚本
├── axure-support.js      # Axure兼容性支持脚本
├── axure-styles.css      # Axure样式修复
├── popup.html            # 弹窗界面
├── popup.css             # 弹窗样式
├── popup.js              # 弹窗脚本
├── icons/                # 图标文件夹
└── README.md             # 说明文档
```

## 开发说明

### 技术栈
- Manifest V3
- Vanilla JavaScript
- Chrome Extension APIs

### 核心原理
1. **检测机制**：通过DOM元素、脚本文件、全局变量等多种方式检测Axure原型
2. **注入机制**：使用Content Script和Web Accessible Resources注入修复代码
3. **兼容性修复**：针对jQuery、CSS、文件访问等问题提供修复方案

### 调试方法
1. 开启扩展的调试模式
2. 查看Chrome开发者工具的Console输出
3. 检查Network面板的资源加载情况

## 常见问题

### Q: 扩展无法检测到Axure原型？
A: 请确保：
- 页面完全加载完成
- 原型文件包含Axure的标准结构
- 没有被其他扩展或脚本干扰

### Q: 本地文件无法正常显示？
A: 请确保：
- Chrome允许扩展访问文件URL
- 在扩展详情页开启"允许访问文件网址"

### Q: 某些交互功能不工作？
A: 请尝试：
- 刷新页面重新加载
- 检查浏览器控制台是否有错误
- 开启调试模式查看详细信息

## 更新日志

### v1.0.0 (2024-08-22)
- 初始版本发布
- 支持Axure原型自动检测
- 实现兼容性修复功能
- 提供用户友好的界面

## 贡献指南

欢迎提交Issue和Pull Request来改进这个扩展！

## 许可证

MIT License

## 联系方式

如有问题或建议，请通过以下方式联系：
- GitHub Issues
- Email: <EMAIL>

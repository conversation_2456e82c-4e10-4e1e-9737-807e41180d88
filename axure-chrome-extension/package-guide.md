# Axure RP Extension 打包和部署指南

## 快速开始

### 1. 准备图标文件
首先需要生成所需的图标文件：

1. 在浏览器中打开 `create-icons.html`
2. 点击各个尺寸下的"下载"按钮
3. 将下载的图标文件保存到 `icons/` 文件夹中：
   - `icon16.png` (16x16像素)
   - `icon32.png` (32x32像素)  
   - `icon48.png` (48x48像素)
   - `icon128.png` (128x128像素)

### 2. 验证文件完整性
确保以下文件都存在且完整：

```
axure-chrome-extension/
├── manifest.json
├── background.js
├── content.js
├── axure-support.js
├── axure-styles.css
├── popup.html
├── popup.css
├── popup.js
├── icons/
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
├── README.md
├── install-guide.md
└── test-page.html
```

## 开发者模式安装

### 步骤1：启用开发者模式
1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"开关

### 步骤2：加载扩展
1. 点击"加载已解压的扩展程序"
2. 选择 `axure-chrome-extension` 文件夹
3. 点击"选择文件夹"

### 步骤3：测试扩展
1. 扩展安装成功后，工具栏会出现扩展图标
2. 打开 `test-page.html` 进行功能测试
3. 点击扩展图标查看检测状态

## 打包为CRX文件

### 方法1：使用Chrome扩展管理页面
1. 在 `chrome://extensions/` 页面
2. 点击"打包扩展程序"按钮
3. 扩展程序根目录：选择 `axure-chrome-extension` 文件夹
4. 私有密钥文件：首次打包留空
5. 点击"打包扩展程序"
6. 生成 `.crx` 文件和 `.pem` 密钥文件

### 方法2：使用命令行工具
```bash
# 安装Chrome扩展打包工具
npm install -g crx3

# 打包扩展
crx3 axure-chrome-extension -o axure-rp-extension.crx
```

## 分发安装包

### 本地分发
1. 将生成的 `.crx` 文件分享给用户
2. 用户将 `.crx` 文件拖拽到Chrome扩展页面
3. 点击"添加扩展程序"确认安装

### 注意事项
- Chrome 73+版本对本地CRX文件有安全限制
- 建议通过开发者模式或Chrome Web Store分发

## 发布到Chrome Web Store

### 准备工作
1. 注册Chrome Web Store开发者账号
2. 支付一次性注册费用（$5）
3. 准备扩展的宣传材料

### 所需材料
1. **扩展包**：打包好的ZIP文件（不是CRX）
2. **图标**：128x128像素的PNG图标
3. **截图**：1280x800或640x400像素的PNG截图
4. **描述文本**：详细的功能描述
5. **分类**：选择"生产力工具"或"开发者工具"

### 发布步骤
1. 访问 [Chrome Web Store Developer Dashboard](https://chrome.google.com/webstore/devconsole)
2. 点击"添加新项"
3. 上传扩展ZIP包
4. 填写扩展信息：
   - 名称：Axure RP Extension
   - 描述：现代Chrome浏览器的Axure原型预览扩展
   - 类别：生产力工具
   - 语言：中文（简体）
5. 上传截图和图标
6. 设置隐私政策（如需要）
7. 提交审核

### 审核时间
- 通常需要1-3个工作日
- 首次发布可能需要更长时间
- 审核通过后自动发布

## 企业内部部署

### 通过组策略部署
1. 创建组策略模板
2. 配置强制安装扩展列表
3. 指定扩展ID和更新URL

### 通过MDM系统部署
1. 配置Chrome管理策略
2. 添加扩展到强制安装列表
3. 推送到目标设备

## 版本更新

### 更新版本号
1. 修改 `manifest.json` 中的 `version` 字段
2. 遵循语义化版本规范（如：1.0.1, 1.1.0, 2.0.0）

### 发布更新
1. **开发者模式**：点击扩展的刷新按钮
2. **CRX文件**：重新打包并分发
3. **Chrome Web Store**：上传新版本，等待审核

## 故障排除

### 常见打包错误
1. **清单文件无效**
   - 检查 `manifest.json` 语法
   - 验证所有引用的文件都存在

2. **图标文件缺失**
   - 确保所有尺寸的图标都存在
   - 检查文件路径是否正确

3. **权限问题**
   - 检查文件夹权限
   - 确保Chrome有读取权限

### 安装问题
1. **扩展无法加载**
   - 检查开发者模式是否开启
   - 查看错误信息并修复

2. **功能不工作**
   - 检查控制台错误信息
   - 验证权限设置
   - 测试在不同页面的兼容性

## 最佳实践

### 代码质量
1. 使用ESLint检查JavaScript代码
2. 压缩CSS和JavaScript文件
3. 优化图标文件大小

### 安全性
1. 最小化权限请求
2. 验证用户输入
3. 使用HTTPS资源

### 性能优化
1. 延迟加载非关键资源
2. 缓存常用数据
3. 优化DOM操作

### 用户体验
1. 提供清晰的状态反馈
2. 支持键盘导航
3. 适配不同屏幕尺寸

## 技术支持

如果在打包或部署过程中遇到问题：

1. 查看Chrome扩展开发文档
2. 检查本项目的GitHub Issues
3. 在开发者社区寻求帮助
4. 联系项目维护者

## 相关链接

- [Chrome扩展开发文档](https://developer.chrome.com/docs/extensions/)
- [Chrome Web Store开发者指南](https://developer.chrome.com/docs/webstore/)
- [Manifest V3迁移指南](https://developer.chrome.com/docs/extensions/mv3/intro/)
- [扩展API参考](https://developer.chrome.com/docs/extensions/reference/)

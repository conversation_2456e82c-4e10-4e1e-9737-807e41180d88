// Manifest V3 version for allowing local file access for Axure prototypes.
// It works by removing the X-Frame-Options header which prevents file:// iframes from loading.
chrome.webRequest.onHeadersReceived.addListener(
  (details) => {
    if (details.responseHeaders) {
        const responseHeaders = details.responseHeaders.filter(
          (header) => header.name.toLowerCase() !== 'x-frame-options'
        );
        return { responseHeaders };
    }
  },
  { urls: ["file:///*"], types: ["sub_frame"] },
  ["blocking", "responseHeaders"]
);

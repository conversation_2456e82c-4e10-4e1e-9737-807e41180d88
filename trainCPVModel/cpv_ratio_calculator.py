#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
CPV零整比计算工具
根据配置项名称计算零整比：同一个配置项名称的CPV占比算术平均值 / 2

作者：AI助手
创建日期：2024
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')


class CPVRatioCalculator:
    """CPV零整比计算器"""
    
    def __init__(self, excel_path):
        """
        初始化计算器
        
        Args:
            excel_path (str): Excel文件路径
        """
        self.excel_path = excel_path
        self.data = None
        self.processed_data = None
        
    def load_data(self):
        """加载Excel数据"""
        try:
            # 检查文件是否存在
            if not os.path.exists(self.excel_path):
                print(f"错误：文件不存在 - {self.excel_path}")
                return False
            
            # 尝试读取Excel文件
            print(f"正在加载数据：{self.excel_path}")
            self.data = pd.read_excel(self.excel_path)
            
            print(f"✓ 成功加载数据，共 {len(self.data)} 行，{len(self.data.columns)} 列")
            print("\n数据列名：")
            for i, col in enumerate(self.data.columns, 1):
                print(f"  {i}. {col}")
            
            print("\n数据预览：")
            print(self.data.head())
            
            return True
            
        except Exception as e:
            print(f"加载数据失败：{str(e)}")
            return False
    
    def check_required_columns(self):
        """检查必需的列是否存在"""
        required_columns = ['配置项名称', 'CPV占比', '车型级别']
        missing_columns = []
        
        # 检查精确匹配
        for col in required_columns:
            if col not in self.data.columns:
                missing_columns.append(col)
        
        if missing_columns:
            print(f"错误：缺少必需的列：{missing_columns}")
            print("\n可用的列名：")
            for col in self.data.columns:
                print(f"  - {col}")
            
            # 尝试模糊匹配
            print("\n尝试模糊匹配相似列名：")
            for missing_col in missing_columns:
                similar_cols = []
                for available_col in self.data.columns:
                    if missing_col in available_col or available_col in missing_col:
                        similar_cols.append(available_col)
                
                if similar_cols:
                    print(f"  '{missing_col}' 可能对应: {similar_cols}")
            
            return False
        
        return True
    
    def analyze_data_structure(self):
        """分析数据结构"""
        if self.data is None:
            print("请先加载数据！")
            return
        
        print("\n" + "="*60)
        print("数据结构分析")
        print("="*60)
        
        # 基本信息
        print(f"总行数：{len(self.data)}")
        print(f"总列数：{len(self.data.columns)}")
        
        # 检查配置项名称列
        if '配置项名称' in self.data.columns:
            config_items = self.data['配置项名称'].value_counts()
            print(f"\n配置项名称统计：")
            print(f"  唯一配置项数量：{len(config_items)}")
            print(f"  平均每个配置项的记录数：{len(self.data) / len(config_items):.2f}")
            
            print(f"\n前10个配置项及其记录数：")
            for item, count in config_items.head(10).items():
                print(f"  {item}: {count} 条记录")
        
        # 检查车型级别列
        if '车型级别' in self.data.columns:
            level_items = self.data['车型级别'].value_counts()
            print(f"\n车型级别统计：")
            print(f"  唯一车型级别数量：{len(level_items)}")
            for level, count in level_items.items():
                print(f"  {level}: {count} 条记录")
        
        # 检查配置项名称和车型级别组合
        if '配置项名称' in self.data.columns and '车型级别' in self.data.columns:
            combo_items = self.data.groupby(['配置项名称', '车型级别']).size()
            print(f"\n配置项名称+车型级别组合统计：")
            print(f"  唯一组合数量：{len(combo_items)}")
            print(f"  平均每个组合的记录数：{len(self.data) / len(combo_items):.2f}")
            
            print(f"\n前10个组合及其记录数：")
            for (config, level), count in combo_items.head(10).items():
                print(f"  {config} + {level}: {count} 条记录")
        
        # 检查CPV占比列
        if 'CPV占比' in self.data.columns:
            cpv_ratio = pd.to_numeric(self.data['CPV占比'], errors='coerce')
            print(f"\nCPV占比统计：")
            print(f"  有效数据点：{cpv_ratio.notna().sum()}")
            print(f"  缺失数据点：{cpv_ratio.isna().sum()}")
            print(f"  数值范围：{cpv_ratio.min():.4f} ~ {cpv_ratio.max():.4f}")
            print(f"  平均值：{cpv_ratio.mean():.4f}")
            print(f"  中位数：{cpv_ratio.median():.4f}")
        
        # 检查缺失值
        print(f"\n缺失值统计：")
        missing_counts = self.data.isnull().sum()
        if missing_counts.sum() > 0:
            for col, count in missing_counts[missing_counts > 0].items():
                print(f"  {col}: {count} 个缺失值 ({count/len(self.data)*100:.1f}%)")
        else:
            print("  无缺失值")
    
    def calculate_ratio(self):
        """计算零整比"""
        if self.data is None:
            print("请先加载数据！")
            return False
        
        if not self.check_required_columns():
            return False
        
        print("\n" + "="*60)
        print("开始计算零整比")
        print("="*60)
        
        # 复制原始数据
        self.processed_data = self.data.copy()
        
        # 将CPV占比转换为数值型
        self.processed_data['CPV占比_数值'] = pd.to_numeric(
            self.processed_data['CPV占比'], errors='coerce'
        )
        
        # 检查转换后的数据
        valid_cpv_count = self.processed_data['CPV占比_数值'].notna().sum()
        print(f"CPV占比有效数据点：{valid_cpv_count}")
        
        if valid_cpv_count == 0:
            print("错误：没有有效的CPV占比数据")
            return False
        
        # 按配置项名称和车型级别组合分组计算平均值
        print("正在按配置项名称和车型级别组合计算CPV占比平均值...")
        
        # 计算每个配置项名称+车型级别组合的CPV占比平均值
        config_avg = self.processed_data.groupby(['配置项名称', '车型级别'])['CPV占比_数值'].agg([
            'mean',  # 平均值
            'count',  # 数量
            'std'    # 标准差
        ]).reset_index()
        
        config_avg.columns = ['配置项名称', '车型级别', 'CPV占比平均值', '记录数量', 'CPV占比标准差']
        
        # 计算零整比 = CPV占比平均值 / 2
        config_avg['零整比'] = config_avg['CPV占比平均值'] / 2
        
        print(f"✓ 计算完成，共处理 {len(config_avg)} 个不同的配置项名称+车型级别组合")
        
        # 显示计算结果统计
        print(f"\n零整比统计：")
        print(f"  最小值：{config_avg['零整比'].min():.6f}")
        print(f"  最大值：{config_avg['零整比'].max():.6f}")
        print(f"  平均值：{config_avg['零整比'].mean():.6f}")
        print(f"  中位数：{config_avg['零整比'].median():.6f}")
        
        # 将零整比合并回原数据
        print("正在将零整比合并到原数据...")
        self.processed_data = self.processed_data.merge(
            config_avg[['配置项名称', '车型级别', '零整比']], 
            on=['配置项名称', '车型级别'], 
            how='left'
        )
        
        # 删除临时列
        if 'CPV占比_数值' in self.processed_data.columns:
            self.processed_data = self.processed_data.drop('CPV占比_数值', axis=1)
        
        print(f"✓ 成功为 {len(self.processed_data)} 行数据添加零整比列")
        
        # 显示前几行结果
        print(f"\n处理结果预览：")
        display_columns = ['配置项名称', '车型级别', 'CPV占比', '零整比']
        available_display_columns = [col for col in display_columns if col in self.processed_data.columns]
        print(self.processed_data[available_display_columns].head(10))
        
        # 保存配置项统计信息
        self.config_stats = config_avg
        
        return True
    
    def show_config_summary(self):
        """显示配置项汇总信息"""
        if not hasattr(self, 'config_stats'):
            print("请先计算零整比！")
            return
        
        print(f"\n" + "="*60)
        print("配置项汇总统计")
        print("="*60)
        
        # 按零整比排序显示
        sorted_stats = self.config_stats.sort_values('零整比', ascending=False)
        
        print(f"{'排名':<4} {'配置项名称':<15} {'车型级别':<10} {'记录数':<6} {'CPV占比均值':<12} {'零整比':<10}")
        print("-" * 80)
        
        for i, (_, row) in enumerate(sorted_stats.head(20).iterrows(), 1):
            config_name = row['配置项名称'][:13] + '...' if len(str(row['配置项名称'])) > 15 else str(row['配置项名称'])
            level_name = row['车型级别'][:8] + '...' if len(str(row['车型级别'])) > 10 else str(row['车型级别'])
            print(f"{i:<4} {config_name:<15} {level_name:<10} {row['记录数量']:<6} {row['CPV占比平均值']:<12.6f} {row['零整比']:<10.6f}")
        
        if len(sorted_stats) > 20:
            print(f"... 还有 {len(sorted_stats) - 20} 个配置项名称+车型级别组合")
    
    def save_results(self, output_path=None):
        """保存处理结果"""
        if self.processed_data is None:
            print("没有处理后的数据可保存！")
            return False
        
        if output_path is None:
            # 生成默认输出文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"CPV数据_已添加零整比_{timestamp}.xlsx"
        
        try:
            print(f"\n正在保存结果到：{output_path}")
            
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 保存主数据（包含零整比）
                self.processed_data.to_excel(
                    writer, 
                    sheet_name='CPV数据_含零整比', 
                    index=False
                )
                
                # 保存配置项统计
                if hasattr(self, 'config_stats'):
                    self.config_stats.sort_values('零整比', ascending=False).to_excel(
                        writer, 
                        sheet_name='配置项统计', 
                        index=False
                    )
                
                # 保存处理日志
                log_data = pd.DataFrame({
                    '处理时间': [datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
                    '原始文件': [self.excel_path],
                    '输出文件': [output_path],
                    '原始行数': [len(self.data) if self.data is not None else 0],
                    '处理后行数': [len(self.processed_data)],
                    '配置项组合数量': [len(self.config_stats) if hasattr(self, 'config_stats') else 0],
                    '计算公式': ['零整比 = 同配置项名称+车型级别组合的CPV占比平均值 / 2']
                })
                log_data.to_excel(writer, sheet_name='处理日志', index=False)
            
            print(f"✓ 结果保存成功！")
            print(f"  主数据表：CPV数据_含零整比 ({len(self.processed_data)} 行)")
            if hasattr(self, 'config_stats'):
                print(f"  统计表：配置项统计 ({len(self.config_stats)} 行配置项名称+车型级别组合)")
            print(f"  处理日志：处理日志")
            
            return True
            
        except Exception as e:
            print(f"保存失败：{str(e)}")
            return False
    
    def validate_results(self):
        """验证计算结果"""
        if self.processed_data is None or not hasattr(self, 'config_stats'):
            print("没有可验证的结果！")
            return
        
        print(f"\n" + "="*60)
        print("结果验证")
        print("="*60)
        
        # 随机选择几个配置项名称+车型级别组合进行验证
        sample_configs = self.config_stats.sample(min(5, len(self.config_stats)))
        
        print("随机验证样本：")
        for _, config_row in sample_configs.iterrows():
            config_name = config_row['配置项名称']
            level_name = config_row['车型级别']
            expected_ratio = config_row['零整比']
            
            # 从原数据中获取该配置项名称+车型级别组合的所有CPV占比
            config_data = self.processed_data[
                (self.processed_data['配置项名称'] == config_name) & 
                (self.processed_data['车型级别'] == level_name)
            ]
            cpv_values = pd.to_numeric(config_data['CPV占比'], errors='coerce').dropna()
            
            if len(cpv_values) > 0:
                calculated_avg = cpv_values.mean()
                calculated_ratio = calculated_avg / 2
                
                print(f"\n配置项：{config_name} + 车型级别：{level_name}")
                print(f"  记录数：{len(cpv_values)}")
                print(f"  CPV占比平均值：{calculated_avg:.6f}")
                print(f"  计算的零整比：{calculated_ratio:.6f}")
                print(f"  保存的零整比：{expected_ratio:.6f}")
                print(f"  验证结果：{'✓ 正确' if abs(calculated_ratio - expected_ratio) < 1e-10 else '✗ 错误'}")


def main():
    """主函数"""
    # Excel文件路径
    excel_path = r"C:\Users\<USER>\Desktop\最终版转换后的CPV数据-OK-全.xlsx"
    
    print("CPV零整比计算工具")
    print("="*50)
    print("计算逻辑：零整比 = 同一个【配置项名称】和【车型级别】组合的CPV占比算术平均值 / 2")
    print("="*50)
    
    # 创建计算器
    calculator = CPVRatioCalculator(excel_path)
    
    # 加载数据
    if not calculator.load_data():
        print("数据加载失败，程序退出")
        return
    
    # 分析数据结构
    calculator.analyze_data_structure()
    
    # 计算零整比
    if not calculator.calculate_ratio():
        print("零整比计算失败，程序退出")
        return
    
    # 显示配置项汇总
    calculator.show_config_summary()
    
    # 验证结果
    calculator.validate_results()
    
    # 保存结果
    if calculator.save_results():
        print("\n" + "="*50)
        print("处理完成！")
        print("="*50)
    else:
        print("保存结果失败！")


if __name__ == "__main__":
    main()
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CatBoost CPV值预测模型
使用CatBoostRegressor根据配置项名称、车型级别、年份、人均GDP、人均可支配收入、CPC预测CPV值

作者: AI Assistant
日期: 2025-01-15
"""

import pandas as pd
import numpy as np
from catboost import CatBoostRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
import os
from datetime import datetime

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class CPVPredictor:
    def __init__(self):
        """初始化CPV预测器"""
        self.model = None
        self.feature_names = None
        self.categorical_features = None
        self.target_column = 'CPV值'
        
        # 定义需要保留的特征列
        self.feature_columns = [
            '配置项名称', '车型级别', '年份', 
            '人均GDP', '人均可支配收入', 'MSRP', 'CPC'
        ]
        
        # 定义需要删除的特征列
        self.columns_to_drop = [
            '千人保有量', '当年千人销量', '类别因子', 
            '类别价格指数', '子项价格指数', 'CPV占比', '零整比'
        ]
        
        # 定义分类特征
        self.categorical_features = ['配置项名称', '车型级别', '年份']
        
        # 模型参数
        self.model_params = {
            'iterations': 3000,
            'learning_rate': 0.03,
            'depth': 6,
            'l2_leaf_reg': 3,
            'bootstrap_type': 'Bayesian',
            'random_seed': 42,
            'od_type': 'Iter',
            'od_wait': 100,
            'verbose': 100,
            'loss_function': 'MAE',
            'eval_metric': 'MAE',
            'task_type': 'CPU',
            'thread_count': -1
        }

    def load_data(self, train_path, test_path):
        """
        步骤1：数据预处理 - 加载训练集和测试集数据
        
        Args:
            train_path: 训练集文件路径
            test_path: 测试集文件路径
            
        Returns:
            合并后的数据集
        """
        print("=" * 50)
        print("步骤1：数据预处理 - 加载数据")
        print("=" * 50)
        
        try:
            # 加载训练集
            print(f"加载训练集: {train_path}")
            train_data = pd.read_excel(train_path)
            train_data['数据类型'] = 'train'
            print(f"训练集形状: {train_data.shape}")
            
            # 加载测试集
            print(f"加载测试集: {test_path}")
            test_data = pd.read_excel(test_path)
            test_data['数据类型'] = 'test'
            print(f"测试集形状: {test_data.shape}")
            
            # 合并数据集
            combined_data = pd.concat([train_data, test_data], ignore_index=True)
            print(f"合并后数据形状: {combined_data.shape}")
            
            # 显示数据基本信息
            print("\n数据列名:")
            print(combined_data.columns.tolist())
            
            print("\n数据基本统计信息:")
            print(combined_data.describe())
            
            print("\n缺失值统计:")
            missing_values = combined_data.isnull().sum()
            if missing_values.sum() > 0:
                print(missing_values[missing_values > 0])
            else:
                print("无缺失值")
                
            return combined_data
            
        except Exception as e:
            print(f"数据加载失败: {str(e)}")
            raise

    def feature_engineering(self, data):
        """
        步骤2：特征工程
        
        Args:
            data: 原始数据
            
        Returns:
            处理后的数据
        """
        print("\n" + "=" * 50)
        print("步骤2：特征工程")
        print("=" * 50)
        
        # 创建数据副本
        processed_data = data.copy()
        
        # 编码分类特征
        print("编码分类特征...")
        for col in self.categorical_features:
            if col in processed_data.columns:
                print(f"处理分类特征: {col}")
                # 将年份转换为字符串类型以便作为分类特征处理
                if col == '年份':
                    processed_data[col] = processed_data[col].astype(str)
                
                # 显示唯一值数量
                unique_count = processed_data[col].nunique()
                print(f"  - {col} 唯一值数量: {unique_count}")
                
                # 转换为category类型
                processed_data[col] = processed_data[col].astype('category')
        
        # 数值特征工程处理
        print("\n处理数值特征...")
        numerical_features = ['人均GDP', '人均可支配收入', 'MSRP', 'CPC']
        
        for col in numerical_features:
            if col in processed_data.columns:
                print(f"处理数值特征: {col}")
                
                # 检查异常值
                Q1 = processed_data[col].quantile(0.25)
                Q3 = processed_data[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                outliers = processed_data[(processed_data[col] < lower_bound) | 
                                        (processed_data[col] > upper_bound)][col]
                
                print(f"  - {col} 异常值数量: {len(outliers)}")
                print(f"  - {col} 范围: [{processed_data[col].min():.2f}, {processed_data[col].max():.2f}]")
        
        return processed_data

    def feature_selection_and_preparation(self, data):
        """
        步骤3：特征选择与数据准备
        
        Args:
            data: 特征工程后的数据
            
        Returns:
            准备好的训练数据和测试数据
        """
        print("\n" + "=" * 50)
        print("步骤3：特征选择与数据准备")
        print("=" * 50)
        
        # 创建数据副本
        prepared_data = data.copy()
        
        # 删除无用特征
        print("删除无用特征...")
        columns_to_remove = []
        for col in self.columns_to_drop:
            if col in prepared_data.columns:
                columns_to_remove.append(col)
                print(f"  - 删除特征: {col}")
        
        if columns_to_remove:
            prepared_data = prepared_data.drop(columns=columns_to_remove)
        
        # 确认分类特征类型
        print("\n确认分类特征类型...")
        for col in self.categorical_features:
            if col in prepared_data.columns:
                if prepared_data[col].dtype.name != 'category':
                    prepared_data[col] = prepared_data[col].astype('category')
                print(f"  - {col}: {prepared_data[col].dtype}")
        
        # 分离训练集和测试集
        train_data = prepared_data[prepared_data['数据类型'] == 'train'].copy()
        test_data = prepared_data[prepared_data['数据类型'] == 'test'].copy()
        
        # 删除数据类型标识列
        train_data = train_data.drop('数据类型', axis=1)
        test_data = test_data.drop('数据类型', axis=1)
        
        print(f"\n最终训练集形状: {train_data.shape}")
        print(f"最终测试集形状: {test_data.shape}")
        
        # 准备特征和目标变量
        X_train = train_data.drop(self.target_column, axis=1)
        y_train = train_data[self.target_column]
        
        # 测试集可能没有目标变量
        if self.target_column in test_data.columns:
            X_test = test_data.drop(self.target_column, axis=1)
            y_test = test_data[self.target_column]
        else:
            X_test = test_data.copy()
            y_test = None
        
        self.feature_names = X_train.columns.tolist()
        print(f"\n最终特征列: {self.feature_names}")
        
        return X_train, y_train, X_test, y_test

    def train_model(self, X_train, y_train, validation_split=0.2):
        """
        步骤4：模型训练
        
        Args:
            X_train: 训练特征
            y_train: 训练目标
            validation_split: 验证集比例
        """
        print("\n" + "=" * 50)
        print("步骤4：模型训练")
        print("=" * 50)
        
        # 分割训练集和验证集
        X_train_split, X_val, y_train_split, y_val = train_test_split(
            X_train, y_train, test_size=validation_split, random_state=42
        )
        
        print(f"训练集形状: {X_train_split.shape}")
        print(f"验证集形状: {X_val.shape}")
        
        # 获取分类特征的索引
        categorical_features_indices = []
        for feature in self.categorical_features:
            if feature in X_train.columns:
                categorical_features_indices.append(X_train.columns.get_loc(feature))
        
        print(f"分类特征索引: {categorical_features_indices}")
        
        # 初始化模型
        self.model = CatBoostRegressor(**self.model_params)
        
        # 训练模型
        print("\n开始训练模型...")
        self.model.fit(
            X_train_split, y_train_split,
            eval_set=(X_val, y_val),
            cat_features=categorical_features_indices,
            use_best_model=True,
            plot=False
        )
        
        print("模型训练完成!")
        
        # 验证集预测和评估
        y_val_pred = self.model.predict(X_val)
        
        val_mae = mean_absolute_error(y_val, y_val_pred)
        val_mse = mean_squared_error(y_val, y_val_pred)
        val_rmse = np.sqrt(val_mse)
        val_r2 = r2_score(y_val, y_val_pred)
        
        print(f"\n验证集性能指标:")
        print(f"MAE: {val_mae:.2f}")
        print(f"MSE: {val_mse:.2f}")
        print(f"RMSE: {val_rmse:.2f}")
        print(f"R²: {val_r2:.4f}")
        
        return y_val, y_val_pred

    def predict(self, X_test):
        """
        使用训练好的模型进行预测
        
        Args:
            X_test: 测试特征
            
        Returns:
            预测结果
        """
        if self.model is None:
            raise ValueError("模型尚未训练，请先调用train_model方法")
        
        print("\n" + "=" * 50)
        print("模型预测")
        print("=" * 50)
        
        predictions = self.model.predict(X_test)
        print(f"预测完成，预测样本数: {len(predictions)}")
        
        return predictions

    def evaluate_model(self, y_true, y_pred, dataset_name="测试集"):
        """
        评估模型性能
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            dataset_name: 数据集名称
        """
        if y_true is None:
            print(f"{dataset_name}没有真实标签，无法进行评估")
            return
        
        mae = mean_absolute_error(y_true, y_pred)
        mse = mean_squared_error(y_true, y_pred)
        rmse = np.sqrt(mse)
        r2 = r2_score(y_true, y_pred)
        
        print(f"\n{dataset_name}性能指标:")
        print(f"MAE: {mae:.2f}")
        print(f"MSE: {mse:.2f}")
        print(f"RMSE: {rmse:.2f}")
        print(f"R²: {r2:.4f}")
        
        return {'MAE': mae, 'MSE': mse, 'RMSE': rmse, 'R2': r2}

    def plot_feature_importance(self, top_n=20, save_path=None):
        """
        绘制特征重要性图
        
        Args:
            top_n: 显示前N个重要特征
            save_path: 保存路径
        """
        if self.model is None:
            print("模型尚未训练，无法显示特征重要性")
            return
        
        # 获取特征重要性
        feature_importance = self.model.get_feature_importance()
        feature_names = self.feature_names
        
        # 创建特征重要性DataFrame
        importance_df = pd.DataFrame({
            'feature': feature_names,
            'importance': feature_importance
        }).sort_values('importance', ascending=False)
        
        # 选择前N个特征
        top_features = importance_df.head(top_n)
        
        # 绘图
        plt.figure(figsize=(12, 8))
        sns.barplot(data=top_features, x='importance', y='feature')
        plt.title(f'特征重要性排序 (Top {top_n})')
        plt.xlabel('重要性')
        plt.ylabel('特征名称')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"特征重要性图已保存至: {save_path}")
        
        plt.show()
        
        return importance_df

    def plot_prediction_analysis(self, y_true, y_pred, dataset_name="验证集", save_path=None):
        """
        绘制预测分析图
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            dataset_name: 数据集名称
            save_path: 保存路径
        """
        if y_true is None:
            print(f"{dataset_name}没有真实标签，无法绘制预测分析图")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 真实值vs预测值散点图
        axes[0, 0].scatter(y_true, y_pred, alpha=0.6)
        axes[0, 0].plot([y_true.min(), y_true.max()], [y_true.min(), y_true.max()], 'r--', lw=2)
        axes[0, 0].set_xlabel('真实值')
        axes[0, 0].set_ylabel('预测值')
        axes[0, 0].set_title(f'{dataset_name} - 真实值 vs 预测值')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 残差图
        residuals = y_true - y_pred
        axes[0, 1].scatter(y_pred, residuals, alpha=0.6)
        axes[0, 1].axhline(y=0, color='r', linestyle='--')
        axes[0, 1].set_xlabel('预测值')
        axes[0, 1].set_ylabel('残差')
        axes[0, 1].set_title(f'{dataset_name} - 残差分析')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 残差直方图
        axes[1, 0].hist(residuals, bins=30, alpha=0.7, edgecolor='black')
        axes[1, 0].set_xlabel('残差')
        axes[1, 0].set_ylabel('频次')
        axes[1, 0].set_title(f'{dataset_name} - 残差分布')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 误差百分比分析
        percentage_error = np.abs(residuals) / y_true * 100
        axes[1, 1].hist(percentage_error, bins=30, alpha=0.7, edgecolor='black')
        axes[1, 1].set_xlabel('绝对百分比误差 (%)')
        axes[1, 1].set_ylabel('频次')
        axes[1, 1].set_title(f'{dataset_name} - 绝对百分比误差分布')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"预测分析图已保存至: {save_path}")
        
        plt.show()

    def save_model(self, model_path):
        """
        保存训练好的模型
        
        Args:
            model_path: 模型保存路径
        """
        if self.model is None:
            print("模型尚未训练，无法保存")
            return
        
        self.model.save_model(model_path)
        print(f"模型已保存至: {model_path}")

    def load_model(self, model_path):
        """
        加载预训练模型
        
        Args:
            model_path: 模型文件路径
        """
        self.model = CatBoostRegressor()
        self.model.load_model(model_path)
        print(f"模型已从 {model_path} 加载")

    def save_predictions(self, X_test, predictions, output_path, y_test=None):
        """
        保存预测结果
        
        Args:
            X_test: 测试特征
            predictions: 预测结果
            output_path: 输出文件路径
            y_test: 测试集真实CPV值（可选）
        """
        # 创建结果DataFrame
        result_df = X_test.copy()
        result_df['CPV值_预测'] = predictions
        
        # 如果有真实CPV值，添加到结果中
        if y_test is not None:
            result_df['CPV值_真实'] = y_test
            # 计算预测误差
            result_df['预测误差'] = result_df['CPV值_真实'] - result_df['CPV值_预测']
            result_df['绝对误差'] = abs(result_df['预测误差'])
            result_df['相对误差(%)'] = (result_df['绝对误差'] / result_df['CPV值_真实']) * 100
            
            print(f"测试集包含真实CPV值，已添加误差分析列")
        
        # 保存到Excel
        result_df.to_excel(output_path, index=False)
        print(f"预测结果已保存至: {output_path}")


def main():
    """主函数"""
    print("CatBoost CPV值预测模型")
    print("=" * 60)
    
    # 数据文件路径
    train_path = r"C:\Users\<USER>\Desktop\CPV数据集-训练.xlsx"
    test_path = r"C:\Users\<USER>\Desktop\CPV数据集-测试.xlsx"
    
    # 检查文件是否存在
    if not os.path.exists(train_path):
        print(f"错误：训练集文件不存在 - {train_path}")
        print("请检查文件路径是否正确")
        return
    
    if not os.path.exists(test_path):
        print(f"错误：测试集文件不存在 - {test_path}")
        print("请检查文件路径是否正确")
        return
    
    # 创建预测器实例
    predictor = CPVPredictor()
    
    try:
        # 步骤1：加载数据
        combined_data = predictor.load_data(train_path, test_path)
        
        # 步骤2：特征工程
        processed_data = predictor.feature_engineering(combined_data)
        
        # 步骤3：特征选择与数据准备
        X_train, y_train, X_test, y_test = predictor.feature_selection_and_preparation(processed_data)
        
        # 步骤4：模型训练
        y_val, y_val_pred = predictor.train_model(X_train, y_train)
        
        # 生成时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 绘制特征重要性
        importance_path = f"feature_importance_{timestamp}.png"
        feature_importance_df = predictor.plot_feature_importance(save_path=importance_path)
        
        # 保存特征重要性到CSV
        importance_csv_path = f"feature_importance_{timestamp}.csv"
        feature_importance_df.to_csv(importance_csv_path, index=False, encoding='utf-8-sig')
        print(f"特征重要性已保存至: {importance_csv_path}")
        
        # 绘制验证集预测分析
        val_analysis_path = f"validation_analysis_{timestamp}.png"
        predictor.plot_prediction_analysis(y_val, y_val_pred, "验证集", val_analysis_path)
        
        # 测试集预测
        test_predictions = predictor.predict(X_test)
        
        # 如果测试集有真实标签，进行评估
        if y_test is not None:
            test_metrics = predictor.evaluate_model(y_test, test_predictions, "测试集")
            
            # 绘制测试集预测分析
            test_analysis_path = f"test_analysis_{timestamp}.png"
            predictor.plot_prediction_analysis(y_test, test_predictions, "测试集", test_analysis_path)
        
        # 保存模型
        model_path = f"cpv_catboost_model_{timestamp}.cbm"
        predictor.save_model(model_path)
        
        # 保存预测结果
        predictions_path = f"cpv_predictions_{timestamp}.xlsx"
        predictor.save_predictions(X_test, test_predictions, predictions_path, y_test)
        
        print("\n" + "=" * 60)
        print("模型训练和预测完成！")
        print(f"模型文件: {model_path}")
        print(f"预测结果: {predictions_path}")
        print(f"特征重要性: {importance_path}")
        print("=" * 60)
        
    except Exception as e:
        print(f"程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CatBoost CPV值预测模型预测脚本
专门用于加载已训练模型进行预测

作者: AI Assistant
日期: 2025-01-15
"""

import os
import sys
import json
import glob
from datetime import datetime
from cpv_base_predictor import CPVBasePredictor

class CPVModelPredictor(CPVBasePredictor):
    """CPV模型预测器"""
    
    def __init__(self):
        super().__init__()
        self.model_info = None
    
    def load_model_with_info(self, model_path=None, model_info_path=None):
        """
        加载模型和相关信息
        
        Args:
            model_path: 模型文件路径
            model_info_path: 模型信息文件路径
        """
        # 如果没有指定路径，尝试找到最新的模型
        if model_path is None:
            model_files = glob.glob("cpv_catboost_model_*.cbm")
            if not model_files:
                raise FileNotFoundError("未找到模型文件，请先训练模型或指定模型路径")
            
            # 按时间戳排序，选择最新的
            model_files.sort(reverse=True)
            model_path = model_files[0]
            print(f"自动选择最新模型: {model_path}")
        
        # 加载模型
        self.load_model(model_path)
        
        # 尝试加载模型信息
        if model_info_path is None:
            # 从模型路径推断信息文件路径
            timestamp = model_path.split('_')[-1].replace('.cbm', '')
            model_info_path = f"model_info_{timestamp}.json"
        
        if os.path.exists(model_info_path):
            with open(model_info_path, 'r', encoding='utf-8') as f:
                self.model_info = json.load(f)
            
            # 从模型信息中恢复特征名称和分类特征
            self.feature_names = self.model_info.get('feature_names', self.feature_names)
            self.categorical_features = self.model_info.get('categorical_features', self.categorical_features)
            
            print(f"已加载模型信息: {model_info_path}")
            print(f"训练样本数: {self.model_info.get('training_samples', '未知')}")
            print(f"特征数量: {len(self.feature_names)}")
        else:
            print(f"警告：未找到模型信息文件 {model_info_path}")
    
    def predict_from_file(self, data_path, output_path=None, has_target=False):
        """
        从文件读取数据并进行预测
        
        Args:
            data_path: 数据文件路径
            output_path: 输出文件路径
            has_target: 数据是否包含目标变量
            
        Returns:
            预测结果
        """
        if self.model is None:
            raise ValueError("模型尚未加载，请先调用load_model_with_info方法")
        
        print("\n" + "=" * 50)
        print("从文件进行预测")
        print("=" * 50)
        
        # 加载数据
        data = self.load_data(data_path)
        
        # 特征工程
        processed_data = self.feature_engineering(data)
        
        # 特征选择与数据准备
        if has_target:
            X, y = self.feature_selection_and_preparation(processed_data, split_data=False)
        else:
            X, y = self.feature_selection_and_preparation(processed_data, split_data=False)
            if y is not None:
                print("检测到目标变量，将进行预测评估")
                has_target = True
        
        # 检查特征一致性
        self._check_feature_consistency(X)
        
        # 进行预测
        print(f"\n开始预测 {len(X)} 个样本...")
        predictions = self.model.predict(X)
        print("预测完成!")
        
        # 如果有目标变量，进行评估
        if has_target and y is not None:
            metrics = self.evaluate_model(y, predictions, "测试数据")
            
            # 绘制预测分析图
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            analysis_path = f"prediction_analysis_{timestamp}.png"
            self.plot_prediction_analysis(y, predictions, "测试数据", analysis_path)
        
        # 保存预测结果
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"predictions_{timestamp}.xlsx"
        
        self.save_predictions(X, predictions, output_path, y if has_target else None)
        
        return predictions, y if has_target else None
    
    def predict_from_dataframe(self, df, has_target=False):
        """
        从DataFrame进行预测
        
        Args:
            df: 输入数据DataFrame
            has_target: 数据是否包含目标变量
            
        Returns:
            预测结果和真实值（如果有）
        """
        if self.model is None:
            raise ValueError("模型尚未加载，请先调用load_model_with_info方法")
        
        print("\n" + "=" * 50)
        print("从DataFrame进行预测")
        print("=" * 50)
        
        # 特征工程
        processed_data = self.feature_engineering(df)
        
        # 特征选择与数据准备
        if has_target:
            X, y = self.feature_selection_and_preparation(processed_data, split_data=False)
        else:
            X, y = self.feature_selection_and_preparation(processed_data, split_data=False)
            if y is not None:
                print("检测到目标变量，将进行预测评估")
                has_target = True
        
        # 检查特征一致性
        self._check_feature_consistency(X)
        
        # 进行预测
        print(f"\n开始预测 {len(X)} 个样本...")
        predictions = self.model.predict(X)
        print("预测完成!")
        
        # 如果有目标变量，进行评估
        if has_target and y is not None:
            metrics = self.evaluate_model(y, predictions, "输入数据")
        
        return predictions, y if has_target else None
    
    def batch_predict(self, data_files, output_dir="batch_predictions"):
        """
        批量预测多个文件
        
        Args:
            data_files: 数据文件路径列表
            output_dir: 输出目录
        """
        if self.model is None:
            raise ValueError("模型尚未加载，请先调用load_model_with_info方法")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        print("\n" + "=" * 50)
        print("批量预测")
        print("=" * 50)
        
        results = {}
        
        for i, data_file in enumerate(data_files, 1):
            print(f"\n处理文件 {i}/{len(data_files)}: {data_file}")
            
            if not os.path.exists(data_file):
                print(f"文件不存在，跳过: {data_file}")
                continue
            
            try:
                # 生成输出文件名
                base_name = os.path.splitext(os.path.basename(data_file))[0]
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_file = os.path.join(output_dir, f"{base_name}_predictions_{timestamp}.xlsx")
                
                # 进行预测
                predictions, y_true = self.predict_from_file(data_file, output_file)
                
                results[data_file] = {
                    'output_file': output_file,
                    'sample_count': len(predictions),
                    'has_target': y_true is not None
                }
                
                print(f"预测完成，结果已保存至: {output_file}")
                
            except Exception as e:
                print(f"处理文件 {data_file} 时出错: {str(e)}")
                results[data_file] = {'error': str(e)}
        
        # 保存批量预测总结
        summary_path = os.path.join(output_dir, f"batch_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n批量预测完成，总结已保存至: {summary_path}")
        return results
    
    def _check_feature_consistency(self, X):
        """
        检查特征一致性
        
        Args:
            X: 输入特征数据
        """
        if self.feature_names is None:
            print("警告：无法检查特征一致性，缺少特征名称信息")
            return
        
        # 检查特征列是否一致
        missing_features = set(self.feature_names) - set(X.columns)
        extra_features = set(X.columns) - set(self.feature_names)
        
        if missing_features:
            raise ValueError(f"缺少必要特征: {missing_features}")
        
        if extra_features:
            print(f"警告：发现额外特征，将被忽略: {extra_features}")
            # 只保留模型需要的特征
            X = X[self.feature_names]
        
        print("特征一致性检查通过")
        return X
    
    def get_model_summary(self):
        """
        获取模型摘要信息
        """
        if self.model is None:
            print("模型尚未加载")
            return
        
        print("\n" + "=" * 50)
        print("模型摘要")
        print("=" * 50)
        
        if self.model_info:
            print(f"模型文件: {self.model_info.get('model_path', '未知')}")
            print(f"训练时间: {self.model_info.get('timestamp', '未知')}")
            print(f"训练样本数: {self.model_info.get('training_samples', '未知')}")
        
        print(f"特征数量: {len(self.feature_names) if self.feature_names else '未知'}")
        print(f"分类特征: {self.categorical_features}")
        
        if self.feature_names:
            print(f"特征列表: {self.feature_names}")
        
        # 显示模型参数
        if self.model_info and 'model_params' in self.model_info:
            print(f"\n模型参数:")
            for key, value in self.model_info['model_params'].items():
                print(f"  {key}: {value}")


def main():
    """主预测函数"""
    print("CatBoost CPV值预测模型 - 预测脚本")
    print("=" * 60)
    
    # 检查命令行参数
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python predict_cpv_model.py <数据文件路径> [模型文件路径]")
        print("  或者直接运行脚本，使用默认设置")
        print()
        
        # 使用默认设置
        data_path = r"D:\08-智见与AI平台\18-WAYS AI\CPV模型训练测试数据\沙特_data_processed_with_matches-OK.xlsx"
        model_path = None  # 自动选择最新模型
    else:
        data_path = sys.argv[1]
        model_path = sys.argv[2] if len(sys.argv) > 2 else None
    
    # 检查数据文件是否存在
    if not os.path.exists(data_path):
        print(f"错误：数据文件不存在 - {data_path}")
        return
    
    try:
        # 创建预测器实例
        predictor = CPVModelPredictor()
        
        # 加载模型
        print("加载模型...")
        predictor.load_model_with_info(model_path)
        
        # 显示模型摘要
        predictor.get_model_summary()
        
        # 进行预测
        predictions, y_true = predictor.predict_from_file(data_path, has_target=True)
        
        print("\n" + "=" * 60)
        print("预测完成！")
        
        # 显示预测统计
        print(f"预测样本数: {len(predictions)}")
        print(f"预测值范围: [{predictions.min():.2f}, {predictions.max():.2f}]")
        print(f"预测值均值: {predictions.mean():.2f}")
        
        if y_true is not None:
            print(f"真实值范围: [{y_true.min():.2f}, {y_true.max():.2f}]")
            print(f"真实值均值: {y_true.mean():.2f}")
        
        print("=" * 60)
        
    except Exception as e:
        print(f"预测过程出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
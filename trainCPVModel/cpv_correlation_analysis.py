#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
CPV相关性分析工具
分析CPV值与人均GDP、人均可支配收入、千人保有量、当年千人销量、装备率、MSRP的相关性

作者：AI助手
创建日期：2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.stats import pearsonr, spearmanr
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


class CPVCorrelationAnalyzer:
    """CPV相关性分析器"""
    
    def __init__(self, excel_path):
        """
        初始化分析器
        
        Args:
            excel_path (str): Excel文件路径
        """
        self.excel_path = excel_path
        self.data = None
        self.correlation_results = {}
        
    def load_data(self):
        """加载Excel数据"""
        try:
            # 尝试读取Excel文件
            self.data = pd.read_excel(self.excel_path)
            print(f"成功加载数据，共 {len(self.data)} 行，{len(self.data.columns)} 列")
            print("\n数据列名：")
            print(self.data.columns.tolist())
            print("\n数据预览：")
            print(self.data.head())
            return True
        except Exception as e:
            print(f"加载数据失败：{str(e)}")
            return False
    
    def preprocess_data(self):
        """数据预处理"""
        if self.data is None:
            print("请先加载数据！")
            return False
        
        # 显示数据信息
        print("\n数据基本信息：")
        print(self.data.info())
        
        # 显示缺失值情况
        print("\n缺失值情况：")
        missing_values = self.data.isnull().sum()
        print(missing_values[missing_values > 0])
        
        # 显示数值型列的统计信息
        print("\n数值型列的统计信息：")
        numeric_columns = self.data.select_dtypes(include=[np.number]).columns
        print(self.data[numeric_columns].describe())
        
        return True
    
    def identify_target_columns(self):
        """识别目标列名（自动匹配相似列名）"""
        target_mapping = {
            'CPV值': ['CPV值', 'CPV', 'cpv值', 'cpv'],
            '人均GDP': ['人均GDP', '人均gdp', 'GDP人均', 'gdp人均', '人均国内生产总值'],
            '人均可支配收入': ['人均可支配收入', '人均收入', '可支配收入', '人均可支配'],
            '千人保有量': ['千人保有量', '保有量', '千人拥有量'],
            '当年千人销量': ['当年千人销量', '千人销量', '销量'],
            '装备率': ['装备率', '配置率'],
            'MSRP': ['MSRP', 'msrp', '建议零售价', '指导价'],
            'CPV占比': ['cpv占比','CPV占比'],
            '零整比': ['零整比'],
            'CPC': ['CPC']
        }
        
        found_columns = {}
        available_columns = self.data.columns.tolist()
        
        print("\n正在匹配目标列名...")
        for target, possible_names in target_mapping.items():
            found = False
            for possible_name in possible_names:
                if possible_name in available_columns:
                    found_columns[target] = possible_name
                    print(f"✓ 找到 {target}: {possible_name}")
                    found = True
                    break
            if not found:
                print(f"✗ 未找到 {target}")
        
        return found_columns
    
    def calculate_correlations(self, column_mapping=None):
        """
        计算相关性
        
        Args:
            column_mapping (dict): 列名映射，如果为None则自动识别
        """
        if self.data is None:
            print("请先加载数据！")
            return
        
        if column_mapping is None:
            column_mapping = self.identify_target_columns()
        
        if 'CPV值' not in column_mapping:
            print("错误：未找到CPV值列！")
            return
        
        cpv_column = column_mapping['CPV值']
        cpv_data = pd.to_numeric(self.data[cpv_column], errors='coerce')
        
        print(f"\n开始计算相关性分析...")
        print(f"CPV列名：{cpv_column}")
        print(f"CPV有效数据点：{cpv_data.notna().sum()}")
        
        correlation_data = []
        
        for target_name, column_name in column_mapping.items():
            if target_name == 'CPV值':
                continue
                
            # 转换为数值型
            target_data = pd.to_numeric(self.data[column_name], errors='coerce')
            
            # 删除缺失值
            valid_mask = cpv_data.notna() & target_data.notna()
            valid_cpv = cpv_data[valid_mask]
            valid_target = target_data[valid_mask]
            
            if len(valid_cpv) < 3:
                print(f"警告：{target_name} 有效数据点太少 ({len(valid_cpv)})，跳过分析")
                continue
            
            # 计算皮尔逊相关系数
            try:
                pearson_corr, pearson_p = pearsonr(valid_cpv, valid_target)
            except:
                pearson_corr, pearson_p = np.nan, np.nan
            
            # 计算斯皮尔曼相关系数
            try:
                spearman_corr, spearman_p = spearmanr(valid_cpv, valid_target)
            except:
                spearman_corr, spearman_p = np.nan, np.nan
            
            # 存储结果
            result = {
                '指标名称': target_name,
                '列名': column_name,
                '有效数据点': len(valid_cpv),
                '皮尔逊相关系数': pearson_corr,
                '皮尔逊P值': pearson_p,
                '斯皮尔曼相关系数': spearman_corr,
                '斯皮尔曼P值': spearman_p
            }
            
            correlation_data.append(result)
            self.correlation_results[target_name] = result
            
            print(f"\n{target_name} ({column_name}):")
            print(f"  有效数据点: {len(valid_cpv)}")
            print(f"  皮尔逊相关系数: {pearson_corr:.4f} (P值: {pearson_p:.4f})")
            print(f"  斯皮尔曼相关系数: {spearman_corr:.4f} (P值: {spearman_p:.4f})")
        
        # 创建结果DataFrame
        self.correlation_df = pd.DataFrame(correlation_data)
        return self.correlation_df
    
    def create_correlation_heatmap(self, column_mapping=None):
        """创建相关性热力图"""
        if column_mapping is None:
            column_mapping = self.identify_target_columns()
        
        # 准备数据
        analysis_columns = []
        analysis_data = []
        
        for target_name, column_name in column_mapping.items():
            if column_name in self.data.columns:
                numeric_data = pd.to_numeric(self.data[column_name], errors='coerce')
                analysis_columns.append(target_name)
                analysis_data.append(numeric_data)
        
        if len(analysis_data) < 2:
            print("可用于分析的数值列太少，无法创建热力图")
            return
        
        # 创建分析DataFrame
        analysis_df = pd.DataFrame(dict(zip(analysis_columns, analysis_data)))
        
        # 计算相关矩阵
        correlation_matrix = analysis_df.corr()
        
        # 创建热力图
        plt.figure(figsize=(10, 8))
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
        
        sns.heatmap(correlation_matrix, 
                   mask=mask,
                   annot=True, 
                   cmap='RdBu_r', 
                   center=0,
                   square=True,
                   fmt='.3f',
                   cbar_kws={"shrink": .8})
        
        plt.title('CPV及相关指标相关性热力图', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.show()
        
        return correlation_matrix
    
    def create_scatter_plots(self, column_mapping=None):
        """创建散点图"""
        if column_mapping is None:
            column_mapping = self.identify_target_columns()
        
        if 'CPV值' not in column_mapping:
            print("未找到CPV值列，无法创建散点图")
            return
        
        cpv_column = column_mapping['CPV值']
        cpv_data = pd.to_numeric(self.data[cpv_column], errors='coerce')
        
        # 计算需要的子图数量
        other_columns = [k for k in column_mapping.keys() if k != 'CPV值']
        n_plots = len(other_columns)
        
        if n_plots == 0:
            print("没有其他指标可以绘制散点图")
            return
        
        # 计算子图布局
        n_cols = min(3, n_plots)
        n_rows = (n_plots + n_cols - 1) // n_cols
        
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 5*n_rows))
        if n_plots == 1:
            axes = [axes]
        elif n_rows == 1:
            axes = axes.flatten()
        else:
            axes = axes.flatten()
        
        for i, (target_name, column_name) in enumerate([(k, v) for k, v in column_mapping.items() if k != 'CPV值']):
            if i >= len(axes):
                break
                
            target_data = pd.to_numeric(self.data[column_name], errors='coerce')
            
            # 删除缺失值
            valid_mask = cpv_data.notna() & target_data.notna()
            valid_cpv = cpv_data[valid_mask]
            valid_target = target_data[valid_mask]
            
            if len(valid_cpv) < 3:
                axes[i].text(0.5, 0.5, f'{target_name}\n数据不足', 
                           ha='center', va='center', transform=axes[i].transAxes)
                axes[i].set_title(target_name)
                continue
            
            # 绘制散点图
            axes[i].scatter(valid_target, valid_cpv, alpha=0.6, s=50)
            
            # 添加趋势线
            try:
                z = np.polyfit(valid_target, valid_cpv, 1)
                p = np.poly1d(z)
                axes[i].plot(valid_target, p(valid_target), "r--", alpha=0.8)
            except:
                pass
            
            # 计算相关系数
            try:
                corr, p_val = pearsonr(valid_target, valid_cpv)
                axes[i].set_title(f'{target_name}\nr={corr:.3f}, p={p_val:.3f}')
            except:
                axes[i].set_title(target_name)
            
            axes[i].set_xlabel(target_name)
            axes[i].set_ylabel('CPV值')
            axes[i].grid(True, alpha=0.3)
        
        # 隐藏多余的子图
        for i in range(n_plots, len(axes)):
            axes[i].set_visible(False)
        
        plt.suptitle('CPV值与各指标散点图分析', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.show()
    
    def generate_report(self):
        """生成分析报告"""
        if not hasattr(self, 'correlation_df'):
            print("请先计算相关性！")
            return
        
        print("\n" + "="*60)
        print("CPV相关性分析报告")
        print("="*60)
        
        print(f"\n数据概览：")
        print(f"- 总样本数：{len(self.data)}")
        print(f"- 分析指标数：{len(self.correlation_df)}")
        
        print(f"\n相关性分析结果：")
        print("-"*60)
        
        # 按相关系数绝对值排序
        sorted_results = self.correlation_df.copy()
        sorted_results['相关系数绝对值'] = abs(sorted_results['皮尔逊相关系数'])
        sorted_results = sorted_results.sort_values('相关系数绝对值', ascending=False)
        
        for _, row in sorted_results.iterrows():
            print(f"\n{row['指标名称']}:")
            print(f"  皮尔逊相关系数: {row['皮尔逊相关系数']:.4f}")
            print(f"  显著性: {'显著' if row['皮尔逊P值'] < 0.05 else '不显著'} (P = {row['皮尔逊P值']:.4f})")
            print(f"  斯皮尔曼相关系数: {row['斯皮尔曼相关系数']:.4f}")
            print(f"  有效数据点: {row['有效数据点']}")
            
            # 相关性强度解释
            abs_corr = abs(row['皮尔逊相关系数'])
            if abs_corr >= 0.7:
                strength = "强相关"
            elif abs_corr >= 0.3:
                strength = "中等相关"
            elif abs_corr >= 0.1:
                strength = "弱相关"
            else:
                strength = "几乎无相关"
            print(f"  相关性强度: {strength}")
        
        print("\n" + "="*60)
        
        # 保存结果到Excel
        output_path = "CPV相关性分析结果.xlsx"
        try:
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                self.correlation_df.to_excel(writer, sheet_name='相关性分析', index=False)
                if hasattr(self, 'data'):
                    # 保存原始数据的数值列统计
                    numeric_cols = self.data.select_dtypes(include=[np.number]).columns
                    self.data[numeric_cols].describe().to_excel(writer, sheet_name='数据统计')
            print(f"分析结果已保存到：{output_path}")
        except Exception as e:
            print(f"保存结果失败：{str(e)}")


def main():
    """主函数"""
    # Excel文件路径
    excel_path = r"C:\Users\<USER>\Desktop\CPV数据_已添加零整比_20250821_151539.xlsx"
    
    print("CPV相关性分析工具")
    print("="*50)
    
    # 创建分析器
    analyzer = CPVCorrelationAnalyzer(excel_path)
    
    # 加载数据
    if not analyzer.load_data():
        print("数据加载失败，程序退出")
        return
    
    # 数据预处理
    analyzer.preprocess_data()
    
    # 计算相关性
    correlation_df = analyzer.calculate_correlations()
    
    if correlation_df is not None and len(correlation_df) > 0:
        # 生成报告
        analyzer.generate_report()
        
        # 创建可视化图表
        print("\n正在生成可视化图表...")
        
        # 相关性热力图
        correlation_matrix = analyzer.create_correlation_heatmap()
        
        # 散点图
        analyzer.create_scatter_plots()
        
        print("\n分析完成！")
    else:
        print("未能完成相关性分析，请检查数据格式和列名")


if __name__ == "__main__":
    main()
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CatBoost CPV值预测模型训练脚本
专门用于训练和验证模型

作者: AI Assistant
日期: 2025-01-15
"""

import os
import sys
from datetime import datetime
from sklearn.model_selection import train_test_split
from catboost import CatBoostRegressor
from cpv_base_predictor import CPVBasePredictor

class CPVModelTrainer(CPVBasePredictor):
    """CPV模型训练器"""
    
    def __init__(self):
        super().__init__()
    
    def train_model(self, X_train, y_train, validation_split=0.2):
        """
        训练CatBoost模型
        
        Args:
            X_train: 训练特征
            y_train: 训练目标
            validation_split: 验证集比例
            
        Returns:
            验证集真实值和预测值
        """
        print("\n" + "=" * 50)
        print("模型训练")
        print("=" * 50)
        
        # 分割训练集和验证集
        X_train_split, X_val, y_train_split, y_val = train_test_split(
            X_train, y_train, test_size=validation_split, random_state=42
        )
        
        print(f"训练集形状: {X_train_split.shape}")
        print(f"验证集形状: {X_val.shape}")
        
        # 获取分类特征的索引
        categorical_features_indices = self.get_categorical_features_indices(X_train)
        print(f"分类特征索引: {categorical_features_indices}")
        
        # 初始化模型
        self.model = CatBoostRegressor(**self.model_params)
        
        # 训练模型
        print("\n开始训练模型...")
        self.model.fit(
            X_train_split, y_train_split,
            eval_set=(X_val, y_val),
            cat_features=categorical_features_indices,
            use_best_model=True,
            plot=False
        )
        
        print("模型训练完成!")
        
        # 验证集预测和评估
        y_val_pred = self.model.predict(X_val)
        
        # 评估模型性能
        self.evaluate_model(y_val, y_val_pred, "验证集")
        
        return y_val, y_val_pred
    
    def train_full_model(self, X_train, y_train):
        """
        使用全部训练数据训练最终模型
        
        Args:
            X_train: 训练特征
            y_train: 训练目标
        """
        print("\n" + "=" * 50)
        print("训练最终模型（使用全部训练数据）")
        print("=" * 50)
        
        # 获取分类特征的索引
        categorical_features_indices = self.get_categorical_features_indices(X_train)
        
        # 初始化模型
        self.model = CatBoostRegressor(**self.model_params)
        
        # 训练模型
        print("开始训练最终模型...")
        self.model.fit(
            X_train, y_train,
            cat_features=categorical_features_indices,
            plot=False
        )
        
        print("最终模型训练完成!")
    
    def cross_validate_model(self, X_train, y_train, cv_folds=5):
        """
        交叉验证模型性能
        
        Args:
            X_train: 训练特征
            y_train: 训练目标
            cv_folds: 交叉验证折数
        """
        from sklearn.model_selection import cross_val_score
        from catboost import cv
        
        print("\n" + "=" * 50)
        print(f"{cv_folds}折交叉验证")
        print("=" * 50)
        
        # 获取分类特征的索引
        categorical_features_indices = self.get_categorical_features_indices(X_train)
        
        # 准备数据池
        from catboost import Pool
        train_pool = Pool(X_train, y_train, cat_features=categorical_features_indices)
        
        # 进行交叉验证
        cv_results = cv(
            train_pool,
            params=self.model_params,
            fold_count=cv_folds,
            shuffle=True,
            partition_random_seed=42,
            plot=False,
            verbose=False
        )
        
        # 获取最后一轮的验证分数
        final_scores = cv_results.iloc[-1]
        
        print(f"交叉验证结果 (最后一轮):")
        for metric_name in final_scores.index:
            if 'test' in metric_name:
                mean_score = final_scores[metric_name]
                print(f"{metric_name}: {mean_score:.4f}")
        
        return cv_results


def main():
    """主训练函数"""
    print("CatBoost CPV值预测模型 - 训练脚本")
    print("=" * 60)
    
    # 数据文件路径
    train_path = r"C:\Users\<USER>\Desktop\CPV数据集-训练.xlsx"
    test_path = r"C:\Users\<USER>\Desktop\CPV数据集-测试.xlsx"
    
    # 检查训练文件是否存在
    if not os.path.exists(train_path):
        print(f"错误：训练集文件不存在 - {train_path}")
        print("请检查文件路径是否正确")
        return
    
    try:
        # 创建训练器实例
        trainer = CPVModelTrainer()
        
        # 是否有测试集
        has_test_set = os.path.exists(test_path)
        
        if has_test_set:
            print("检测到测试集，将进行完整的训练和评估流程")
            # 加载训练集和测试集
            combined_data = trainer.load_data(train_path, test_path)
            
            # 特征工程
            processed_data = trainer.feature_engineering(combined_data)
            
            # 特征选择与数据准备
            X_train, y_train, X_test, y_test = trainer.feature_selection_and_preparation(processed_data)
        else:
            print("未检测到测试集，仅使用训练集进行训练和验证")
            # 仅加载训练集
            train_data = trainer.load_data(train_path)
            
            # 特征工程
            processed_data = trainer.feature_engineering(train_data)
            
            # 特征选择与数据准备
            X_train, y_train = trainer.feature_selection_and_preparation(processed_data, split_data=False)
            X_test, y_test = None, None
        
        # 生成时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 1. 进行交叉验证
        print("\n执行交叉验证...")
        cv_results = trainer.cross_validate_model(X_train, y_train, cv_folds=5)
        
        # 保存交叉验证结果
        cv_results_path = f"cv_results_{timestamp}.csv"
        cv_results.to_csv(cv_results_path, index=False, encoding='utf-8-sig')
        print(f"交叉验证结果已保存至: {cv_results_path}")
        
        # 2. 训练模型并在验证集上评估
        print("\n执行训练和验证...")
        y_val, y_val_pred = trainer.train_model(X_train, y_train)
        
        # 绘制验证集预测分析
        val_analysis_path = f"validation_analysis_{timestamp}.png"
        trainer.plot_prediction_analysis(y_val, y_val_pred, "验证集", val_analysis_path)
        
        # 3. 训练最终模型（使用全部训练数据）
        print("\n训练最终模型...")
        trainer.train_full_model(X_train, y_train)
        
        # 4. 绘制特征重要性
        importance_path = f"feature_importance_{timestamp}.png"
        feature_importance_df = trainer.plot_feature_importance(save_path=importance_path)
        
        # 保存特征重要性到CSV
        importance_csv_path = f"feature_importance_{timestamp}.csv"
        feature_importance_df.to_csv(importance_csv_path, index=False, encoding='utf-8-sig')
        print(f"特征重要性已保存至: {importance_csv_path}")
        
        # 5. 如果有测试集，进行测试集评估
        if has_test_set and X_test is not None:
            print("\n在测试集上进行评估...")
            test_predictions = trainer.model.predict(X_test)
            
            # 评估测试集性能
            if y_test is not None:
                test_metrics = trainer.evaluate_model(y_test, test_predictions, "测试集")
                
                # 绘制测试集预测分析
                test_analysis_path = f"test_analysis_{timestamp}.png"
                trainer.plot_prediction_analysis(y_test, test_predictions, "测试集", test_analysis_path)
            
            # 保存测试集预测结果
            test_predictions_path = f"test_predictions_{timestamp}.xlsx"
            trainer.save_predictions(X_test, test_predictions, test_predictions_path, y_test)
        
        # 6. 保存最终模型
        model_path = f"cpv_catboost_model_{timestamp}.cbm"
        trainer.save_model(model_path)
        
        # 7. 保存模型信息
        model_info = {
            'model_path': model_path,
            'timestamp': timestamp,
            'feature_names': trainer.feature_names,
            'categorical_features': trainer.categorical_features,
            'model_params': trainer.model_params,
            'training_samples': len(X_train)
        }
        
        import json
        model_info_path = f"model_info_{timestamp}.json"
        with open(model_info_path, 'w', encoding='utf-8') as f:
            json.dump(model_info, f, ensure_ascii=False, indent=2)
        print(f"模型信息已保存至: {model_info_path}")
        
        print("\n" + "=" * 60)
        print("模型训练完成！")
        print(f"最终模型: {model_path}")
        print(f"模型信息: {model_info_path}")
        print(f"特征重要性: {importance_path}")
        print(f"交叉验证结果: {cv_results_path}")
        if has_test_set and X_test is not None:
            print(f"测试集预测: {test_predictions_path}")
        print("=" * 60)
        
    except Exception as e:
        print(f"训练过程出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
import pygame
import sys
import math
import numpy as np
from pygame.locals import *

# 初始化Pygame
pygame.init()

# 屏幕设置
WIDTH, HEIGHT = 800, 600
screen = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("旋转正十二边形内弹跳小球")

# 颜色定义
BACKGROUND = (20, 20, 30)
POLYGON_COLOR = (100, 150, 255)
BALL_COLOR = (50, 220, 100)
TRAIL_COLOR = (50, 220, 100, 100)
WHITE = (255, 255, 255)
GRAY = (180, 180, 180)

# 物理参数
FPS = 60
GRAVITY = 0.2
ELASTICITY = 0.85
FRICTION = 0.995
ROTATION_SPEED = 0.005

# 十二边形参数
SIDES = 12
POLYGON_RADIUS = 250

# 小球参数
BALL_RADIUS = 12
BALL_INITIAL_VELOCITY = [2, -3]


# 创建正多边形的顶点
def create_regular_polygon(center_x, center_y, radius, sides, rotation=0):
    points = []
    for i in range(sides):
        angle = rotation + i * (2 * math.pi / sides)
        x = center_x + radius * math.cos(angle)
        y = center_y + radius * math.sin(angle)
        points.append((x, y))
    return points


# 绘制正多边形
def draw_polygon(surface, points, color, width=2):
    pygame.draw.polygon(surface, color, points, width)


# 检测小球与线段的碰撞
def ball_line_collision(ball_pos, ball_radius, line_start, line_end):
    # 线段向量
    line_vec = (line_end[0] - line_start[0], line_end[1] - line_start[1])
    line_length = math.sqrt(line_vec[0] ** 2 + line_vec[1] ** 2)

    if line_length == 0:
        return False, None, None

    line_unit = (line_vec[0] / line_length, line_vec[1] / line_length)

    # 小球到线段起点的向量
    ball_to_start = (ball_pos[0] - line_start[0], ball_pos[1] - line_start[1])

    # 投影长度
    projection_length = ball_to_start[0] * line_unit[0] + ball_to_start[1] * line_unit[1]

    # 确保投影点在线段上
    if projection_length < 0:
        closest_point = line_start
    elif projection_length > line_length:
        closest_point = line_end
    else:
        closest_point = (
            line_start[0] + line_unit[0] * projection_length,
            line_start[1] + line_unit[1] * projection_length
        )

    # 计算小球到最近点的距离
    distance = math.sqrt(
        (ball_pos[0] - closest_point[0]) ** 2 +
        (ball_pos[1] - closest_point[1]) ** 2
    )

    # 如果距离小于小球半径，则发生碰撞
    if distance < ball_radius:
        # 计算法线向量（从碰撞点指向小球中心）
        if distance == 0:  # 避免除以零
            normal = (1, 0)
        else:
            normal = (
                (ball_pos[0] - closest_point[0]) / distance,
                (ball_pos[1] - closest_point[1]) / distance
            )
        return True, normal, closest_point

    return False, None, None


# 计算旋转面的切向速度
def calculate_tangential_velocity(center, point, rotation_speed, normal):
    # 计算从中心到碰撞点的向量
    to_point = (point[0] - center[0], point[1] - center[1])
    distance = math.sqrt(to_point[0] ** 2 + to_point[1] ** 2)

    if distance == 0:
        return (0, 0)

    # 单位向量
    to_point_unit = (to_point[0] / distance, to_point[1] / distance)

    # 切向方向（垂直于半径方向）
    tangent = (-to_point_unit[1], to_point_unit[0])

    # 切向速度大小
    tangential_speed = rotation_speed * distance

    # 切向速度向量
    tangential_velocity = (
        tangent[0] * tangential_speed,
        tangent[1] * tangential_speed
    )

    # 计算切向速度在法线垂直方向上的分量
    tangent_dot_normal = tangent[0] * normal[0] + tangent[1] * normal[1]

    # 返回切向速度在法线垂直方向上的投影
    return (
        tangential_velocity[0] - tangent_dot_normal * normal[0],
        tangential_velocity[1] - tangent_dot_normal * normal[1]
    )


# 检查点是否在多边形内部
def point_in_polygon(point, polygon):
    x, y = point
    n = len(polygon)
    inside = False

    p1x, p1y = polygon[0]
    for i in range(n + 1):
        p2x, p2y = polygon[i % n]
        if y > min(p1y, p2y):
            if y <= max(p1y, p2y):
                if x <= max(p1x, p2x):
                    if p1y != p2y:
                        xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                    if p1x == p2x or x <= xinters:
                        inside = not inside
        p1x, p1y = p2x, p2y

    return inside


# 主函数
def main():
    clock = pygame.time.Clock()

    # 初始化多边形
    polygon_center = (WIDTH // 2, HEIGHT // 2)
    polygon_rotation = 0

    # 初始化小球
    ball_pos = [WIDTH // 2, HEIGHT // 2 - 100]
    ball_vel = BALL_INITIAL_VELOCITY.copy()

    # 轨迹点列表
    trail_points = []

    # 显示物理参数
    show_physics_info = True

    # 主循环
    running = True
    while running:
        for event in pygame.event.get():
            if event.type == QUIT:
                running = False
            elif event.type == KEYDOWN:
                if event.key == K_SPACE:
                    # 空格键重置小球位置和速度
                    ball_pos = [WIDTH // 2, HEIGHT // 2 - 100]
                    ball_vel = BALL_INITIAL_VELOCITY.copy()
                    trail_points = []
                elif event.key == K_i:
                    # i键切换物理信息显示
                    show_physics_info = not show_physics_info

        # 更新多边形旋转
        polygon_rotation += ROTATION_SPEED

        # 创建多边形
        polygon_points = create_regular_polygon(
            polygon_center[0], polygon_center[1],
            POLYGON_RADIUS, SIDES, polygon_rotation
        )

        # 应用重力
        ball_vel[1] += GRAVITY

        # 更新小球位置
        ball_pos[0] += ball_vel[0]
        ball_pos[1] += ball_vel[1]

        # 添加轨迹点
        trail_points.append((int(ball_pos[0]), int(ball_pos[1])))
        if len(trail_points) > 50:
            trail_points.pop(0)

        # 检测碰撞
        collided = False

        # 与多边形的碰撞检测
        for i in range(SIDES):
            start = polygon_points[i]
            end = polygon_points[(i + 1) % SIDES]

            collision, normal, point = ball_line_collision(ball_pos, BALL_RADIUS, start, end)
            if collision:
                collided = True

                # 计算切向速度
                tangential_vel = calculate_tangential_velocity(
                    polygon_center, point, ROTATION_SPEED, normal
                )

                # 反弹
                dot_product = ball_vel[0] * normal[0] + ball_vel[1] * normal[1]
                ball_vel[0] = (ball_vel[0] - 2 * dot_product * normal[0]) * ELASTICITY
                ball_vel[1] = (ball_vel[1] - 2 * dot_product * normal[1]) * ELASTICITY

                # 添加切向速度
                ball_vel[0] += tangential_vel[0]
                ball_vel[1] += tangential_vel[1]

                # 稍微将小球推离边界以防止卡住
                ball_pos[0] += normal[0] * 2
                ball_pos[1] += normal[1] * 2

                break

        # 确保小球在多边形内部
        if not point_in_polygon(ball_pos, polygon_points):
            # 如果小球跑出了多边形，将其拉回中心
            direction = [
                polygon_center[0] - ball_pos[0],
                polygon_center[1] - ball_pos[1]
            ]
            length = math.sqrt(direction[0] ** 2 + direction[1] ** 2)
            if length > 0:
                direction[0] /= length
                direction[1] /= length

            ball_pos[0] += direction[0] * 5
            ball_pos[1] += direction[1] * 5

            # 减小速度
            ball_vel[0] *= 0.5
            ball_vel[1] *= 0.5

        # 应用摩擦力
        ball_vel[0] *= FRICTION
        ball_vel[1] *= FRICTION

        # 绘制背景
        screen.fill(BACKGROUND)

        # 绘制轨迹
        for i, point in enumerate(trail_points):
            alpha = int(255 * i / len(trail_points))
            radius = int(BALL_RADIUS * 0.5 * i / len(trail_points))
            pygame.draw.circle(screen, (TRAIL_COLOR[0], TRAIL_COLOR[1], TRAIL_COLOR[2], alpha),
                               point, max(1, radius))

        # 绘制多边形
        draw_polygon(screen, polygon_points, POLYGON_COLOR, 3)

        # 绘制小球
        pygame.draw.circle(screen, BALL_COLOR, (int(ball_pos[0]), int(ball_pos[1])), BALL_RADIUS)
        pygame.draw.circle(screen, WHITE, (int(ball_pos[0]), int(ball_pos[1])), BALL_RADIUS, 1)

        # 绘制中心点
        pygame.draw.circle(screen, WHITE, polygon_center, 5)

        # 显示物理信息
        if show_physics_info:
            font = pygame.font.SysFont(None, 24)
            speed = math.sqrt(ball_vel[0] ** 2 + ball_vel[1] ** 2)
            info_text = [
                f"小球速度: {speed:.2f}",
                f"X速度: {ball_vel[0]:.2f}",
                f"Y速度: {ball_vel[1]:.2f}",
                f"位置: ({ball_pos[0]:.1f}, {ball_pos[1]:.1f})",
                f"多边形旋转: {polygon_rotation % (2 * math.pi):.2f} rad",
                "按空格键重置小球",
                "按I键切换信息显示"
            ]

            for i, text in enumerate(info_text):
                text_surface = font.render(text, True, WHITE)
                screen.blit(text_surface, (10, 10 + i * 25))

        pygame.display.flip()
        clock.tick(FPS)

    pygame.quit()
    sys.exit()


if __name__ == "__main__":
    main()
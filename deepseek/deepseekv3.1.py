import pygame
import sys
import math
import numpy as np
from pygame.locals import *

# 初始化Pygame
pygame.init()

# 屏幕设置
WIDTH, HEIGHT = 800, 600
screen = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("交叉旋转正六边形物理模拟")

# 颜色定义
BACKGROUND = (10, 10, 40)
BLUE = (64, 128, 255)
GREEN = (64, 220, 128)
RED = (255, 64, 64)
PURPLE = (180, 64, 255)
TRAIL_COLOR = (255, 128, 128, 100)
WHITE = (255, 255, 255)
GRAY = (180, 180, 180)

# 物理参数
FPS = 60
GRAVITY = 0.2
ELASTICITY = 0.8
FRICTION = 0.99
ROTATION_SPEED = 0.01

# 六边形参数
HEX_RADIUS = 150
HEX_OFFSET = HEX_RADIUS * 0.6  # 控制重叠程度

# 小球参数
BALL_RADIUS = 10
BALL_COLOR = RED


# 创建正六边形的顶点
def create_hexagon(center_x, center_y, radius, rotation=0):
    points = []
    for i in range(6):
        angle = rotation + i * (2 * math.pi / 6)
        x = center_x + radius * math.cos(angle)
        y = center_y + radius * math.sin(angle)
        points.append((x, y))
    return points


# 绘制正六边形
def draw_hexagon(surface, points, color, width=2):
    pygame.draw.polygon(surface, color, points, width)


# 检测小球与线段的碰撞
def ball_line_collision(ball_pos, ball_radius, line_start, line_end):
    # 线段向量
    line_vec = (line_end[0] - line_start[0], line_end[1] - line_start[1])
    line_length = math.sqrt(line_vec[0] ** 2 + line_vec[1] ** 2)
    line_unit = (line_vec[0] / line_length, line_vec[1] / line_length)

    # 小球到线段起点的向量
    ball_to_start = (ball_pos[0] - line_start[0], ball_pos[1] - line_start[1])

    # 投影长度
    projection_length = ball_to_start[0] * line_unit[0] + ball_to_start[1] * line_unit[1]

    # 确保投影点在线段上
    if projection_length < 0:
        closest_point = line_start
    elif projection_length > line_length:
        closest_point = line_end
    else:
        closest_point = (
            line_start[0] + line_unit[0] * projection_length,
            line_start[1] + line_unit[1] * projection_length
        )

    # 计算小球到最近点的距离
    distance = math.sqrt(
        (ball_pos[0] - closest_point[0]) ** 2 +
        (ball_pos[1] - closest_point[1]) ** 2
    )

    # 如果距离小于小球半径，则发生碰撞
    if distance < ball_radius:
        # 计算法线向量（从碰撞点指向小球中心）
        if distance == 0:  # 避免除以零
            normal = (1, 0)
        else:
            normal = (
                (ball_pos[0] - closest_point[0]) / distance,
                (ball_pos[1] - closest_point[1]) / distance
            )
        return True, normal, closest_point

    return False, None, None


# 计算旋转面的切向速度
def calculate_tangential_velocity(center, point, rotation_speed, normal):
    # 计算从中心到碰撞点的向量
    to_point = (point[0] - center[0], point[1] - center[1])
    distance = math.sqrt(to_point[0] ** 2 + to_point[1] ** 2)

    if distance == 0:
        return (0, 0)

    # 单位向量
    to_point_unit = (to_point[0] / distance, to_point[1] / distance)

    # 切向方向（垂直于半径方向）
    tangent = (-to_point_unit[1], to_point_unit[0])

    # 切向速度大小
    tangential_speed = rotation_speed * distance

    # 切向速度向量
    tangential_velocity = (
        tangent[0] * tangential_speed,
        tangent[1] * tangential_speed
    )

    # 计算切向速度在法线垂直方向上的分量
    tangent_dot_normal = tangent[0] * normal[0] + tangent[1] * normal[1]

    # 返回切向速度在法线垂直方向上的投影
    return (
        tangential_velocity[0] - tangent_dot_normal * normal[0],
        tangential_velocity[1] - tangent_dot_normal * normal[1]
    )


# 主函数
def main():
    clock = pygame.time.Clock()

    # 初始化六边形
    hex1_center = (WIDTH // 2 - HEX_OFFSET, HEIGHT // 2)
    hex2_center = (WIDTH // 2 + HEX_OFFSET, HEIGHT // 2)
    hex1_rotation = 0
    hex2_rotation = 0

    # 初始化小球
    ball_pos = [WIDTH // 2, HEIGHT // 2]
    ball_vel = [0, 0]

    # 轨迹点列表
    trail_points = []

    # 显示物理参数
    show_physics_info = True

    # 主循环
    running = True
    while running:
        for event in pygame.event.get():
            if event.type == QUIT:
                running = False
            elif event.type == KEYDOWN:
                if event.key == K_SPACE:
                    # 空格键重置小球位置和速度
                    ball_pos = [WIDTH // 2, HEIGHT // 2]
                    ball_vel = [0, 0]
                    trail_points = []
                elif event.key == K_i:
                    # i键切换物理信息显示
                    show_physics_info = not show_physics_info

        # 更新六边形旋转
        hex1_rotation -= ROTATION_SPEED  # 逆时针旋转
        hex2_rotation += ROTATION_SPEED  # 顺时针旋转

        # 创建六边形
        hex1_points = create_hexagon(hex1_center[0], hex1_center[1], HEX_RADIUS, hex1_rotation)
        hex2_points = create_hexagon(hex2_center[0], hex2_center[1], HEX_RADIUS, hex2_rotation)

        # 应用重力
        ball_vel[1] += GRAVITY

        # 更新小球位置
        ball_pos[0] += ball_vel[0]
        ball_pos[1] += ball_vel[1]

        # 添加轨迹点
        trail_points.append((int(ball_pos[0]), int(ball_pos[1])))
        if len(trail_points) > 100:
            trail_points.pop(0)

        # 检测碰撞
        collided = False

        # 与第一个六边形的碰撞检测
        for i in range(6):
            start = hex1_points[i]
            end = hex1_points[(i + 1) % 6]

            collision, normal, point = ball_line_collision(ball_pos, BALL_RADIUS, start, end)
            if collision:
                collided = True

                # 计算切向速度
                tangential_vel = calculate_tangential_velocity(hex1_center, point, -ROTATION_SPEED, normal)

                # 反弹
                dot_product = ball_vel[0] * normal[0] + ball_vel[1] * normal[1]
                ball_vel[0] = (ball_vel[0] - 2 * dot_product * normal[0]) * ELASTICITY
                ball_vel[1] = (ball_vel[1] - 2 * dot_product * normal[1]) * ELASTICITY

                # 添加切向速度
                ball_vel[0] += tangential_vel[0]
                ball_vel[1] += tangential_vel[1]

                # 稍微将小球推离边界以防止卡住
                ball_pos[0] += normal[0] * 2
                ball_pos[1] += normal[1] * 2

                break

        # 与第二个六边形的碰撞检测
        if not collided:
            for i in range(6):
                start = hex2_points[i]
                end = hex2_points[(i + 1) % 6]

                collision, normal, point = ball_line_collision(ball_pos, BALL_RADIUS, start, end)
                if collision:
                    collided = True

                    # 计算切向速度
                    tangential_vel = calculate_tangential_velocity(hex2_center, point, ROTATION_SPEED, normal)

                    # 反弹
                    dot_product = ball_vel[0] * normal[0] + ball_vel[1] * normal[1]
                    ball_vel[0] = (ball_vel[0] - 2 * dot_product * normal[0]) * ELASTICITY
                    ball_vel[1] = (ball_vel[1] - 2 * dot_product * normal[1]) * ELASTICITY

                    # 添加切向速度
                    ball_vel[0] += tangential_vel[0]
                    ball_vel[1] += tangential_vel[1]

                    # 稍微将小球推离边界以防止卡住
                    ball_pos[0] += normal[0] * 2
                    ball_pos[1] += normal[1] * 2

                    break

        # 应用摩擦力
        ball_vel[0] *= FRICTION
        ball_vel[1] *= FRICTION

        # 绘制背景
        screen.fill(BACKGROUND)

        # 绘制重叠区域（近似）
        overlap_points = []
        for point in hex1_points:
            if (hex2_center[0] - HEX_RADIUS < point[0] < hex2_center[0] + HEX_RADIUS and
                    hex2_center[1] - HEX_RADIUS < point[1] < hex2_center[1] + HEX_RADIUS):
                overlap_points.append(point)
        for point in hex2_points:
            if (hex1_center[0] - HEX_RADIUS < point[0] < hex1_center[0] + HEX_RADIUS and
                    hex1_center[1] - HEX_RADIUS < point[1] < hex1_center[1] + HEX_RADIUS):
                overlap_points.append(point)

        if len(overlap_points) >= 3:
            pygame.draw.polygon(screen, PURPLE, overlap_points, 0)
            pygame.draw.polygon(screen, PURPLE, overlap_points, 2)

        # 绘制轨迹
        for i, point in enumerate(trail_points):
            alpha = int(255 * i / len(trail_points))
            radius = int(BALL_RADIUS * 0.5 * i / len(trail_points))
            pygame.draw.circle(screen, (TRAIL_COLOR[0], TRAIL_COLOR[1], TRAIL_COLOR[2], alpha),
                               point, max(1, radius))

        # 绘制六边形
        draw_hexagon(screen, hex1_points, BLUE, 3)
        draw_hexagon(screen, hex2_points, GREEN, 3)

        # 绘制小球
        pygame.draw.circle(screen, BALL_COLOR, (int(ball_pos[0]), int(ball_pos[1])), BALL_RADIUS)
        pygame.draw.circle(screen, WHITE, (int(ball_pos[0]), int(ball_pos[1])), BALL_RADIUS, 1)

        # 显示物理信息
        if show_physics_info:
            font = pygame.font.SysFont(None, 24)
            speed = math.sqrt(ball_vel[0] ** 2 + ball_vel[1] ** 2)
            info_text = [
                f"小球速度: {speed:.2f}",
                f"X速度: {ball_vel[0]:.2f}",
                f"Y速度: {ball_vel[1]:.2f}",
                f"位置: ({ball_pos[0]:.1f}, {ball_pos[1]:.1f})",
                "按空格键重置小球",
                "按I键切换信息显示"
            ]

            for i, text in enumerate(info_text):
                text_surface = font.render(text, True, WHITE)
                screen.blit(text_surface, (10, 10 + i * 25))

        pygame.display.flip()
        clock.tick(FPS)

    pygame.quit()
    sys.exit()


if __name__ == "__main__":
    main()
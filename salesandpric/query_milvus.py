#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从本地Milvus向量数据库中读取和查询知识片段
支持向量相似度搜索和关键词过滤
"""

import os
import requests
import json
from typing import List, Dict, Any, Optional
from pymilvus import (
    connections,
    Collection,
    utility
)
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BrandKnowledgeQuery:
    def __init__(self):
        # 嵌入模型配置
        self.api_base = "https://api.siliconflow.cn/v1/embeddings"
        self.api_key = "sk-eqxovpsvjjcbrxelpiuxpvdwjfsaurjpfipfvtgnzlsphlty"
        self.model_name = "BAAI/bge-m3"
        
        # Milvus配置
        self.milvus_host = "localhost"
        self.milvus_port = "19530"
        self.collection_name = "brand_knowledge"
        self.collection = None
        
    def connect_milvus(self):
        """
        连接到Milvus数据库并加载集合
        """
        try:
            connections.connect("default", host=self.milvus_host, port=self.milvus_port)
            logger.info(f"成功连接到Milvus: {self.milvus_host}:{self.milvus_port}")
            
            # 检查集合是否存在
            if not utility.has_collection(self.collection_name):
                logger.error(f"集合 {self.collection_name} 不存在")
                raise ValueError(f"集合 {self.collection_name} 不存在，请先运行 load_to_milvus.py 创建数据")
            
            # 加载集合
            self.collection = Collection(self.collection_name)
            self.collection.load()
            logger.info(f"成功加载集合: {self.collection_name}")
            
        except Exception as e:
            logger.error(f"连接Milvus失败: {e}")
            raise
    
    def get_embedding(self, text: str) -> List[float]:
        """
        获取单个文本的嵌入向量
        """
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.model_name,
            "input": [text]
        }
        
        try:
            response = requests.post(self.api_base, headers=headers, json=data)
            response.raise_for_status()
            
            result = response.json()
            embedding = result["data"][0]["embedding"]
            logger.debug(f"成功获取文本嵌入向量")
            return embedding
            
        except requests.exceptions.RequestException as e:
            logger.error(f"调用嵌入模型API失败: {e}")
            raise
        except KeyError as e:
            logger.error(f"解析API响应失败: {e}")
            raise
    
    def semantic_search(self, query_text: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        基于语义相似度的向量搜索
        
        Args:
            query_text: 查询文本
            top_k: 返回结果数量
            
        Returns:
            搜索结果列表，包含文本内容和相似度分数
        """
        if not self.collection:
            raise ValueError("请先调用 connect_milvus() 连接数据库")
        
        try:
            # 获取查询文本的嵌入向量
            query_embedding = self.get_embedding(query_text)
            
            # 设置搜索参数
            search_params = {"metric_type": "IP", "params": {"nprobe": 10}}
            
            # 执行向量搜索
            results = self.collection.search(
                data=[query_embedding],
                anns_field="embedding",
                param=search_params,
                limit=top_k,
                output_fields=["text"]
            )
            
            # 处理搜索结果
            search_results = []
            for hits in results:
                for hit in hits:
                    search_results.append({
                        "id": hit.id,
                        "text": hit.entity.get("text"),
                        "score": hit.score,
                        "distance": hit.distance
                    })
            
            logger.info(f"语义搜索完成，返回 {len(search_results)} 个结果")
            return search_results
            
        except Exception as e:
            logger.error(f"语义搜索失败: {e}")
            raise
    
    def keyword_search(self, keyword: str, top_k: int = 10) -> List[Dict[str, Any]]:
        """
        基于关键词的文本搜索
        
        Args:
            keyword: 搜索关键词
            top_k: 返回结果数量
            
        Returns:
            包含关键词的文本列表
        """
        if not self.collection:
            raise ValueError("请先调用 connect_milvus() 连接数据库")
        
        try:
            # 构建查询表达式
            expr = f'text like "%{keyword}%"'
            
            # 执行查询
            results = self.collection.query(
                expr=expr,
                output_fields=["id", "text"],
                limit=top_k
            )
            
            # 处理查询结果
            search_results = []
            for result in results:
                search_results.append({
                    "id": result["id"],
                    "text": result["text"]
                })
            
            logger.info(f"关键词搜索完成，找到 {len(search_results)} 个匹配结果")
            return search_results
            
        except Exception as e:
            logger.error(f"关键词搜索失败: {e}")
            raise
    
    def get_random_samples(self, count: int = 5) -> List[Dict[str, Any]]:
        """
        随机获取一些知识片段样本
        
        Args:
            count: 样本数量
            
        Returns:
            随机样本列表
        """
        if not self.collection:
            raise ValueError("请先调用 connect_milvus() 连接数据库")
        
        try:
            # 获取集合中的总数据量
            total_count = self.collection.num_entities
            logger.info(f"集合中共有 {total_count} 条数据")
            
            if total_count == 0:
                logger.warning("集合中没有数据")
                return []
            
            # 随机查询一些数据
            import random
            random_ids = random.sample(range(1, min(total_count + 1, 1000)), min(count, total_count))
            
            # 构建查询表达式
            expr = f'id in {random_ids}'
            
            # 执行查询
            results = self.collection.query(
                expr=expr,
                output_fields=["id", "text"]
            )
            
            logger.info(f"成功获取 {len(results)} 个随机样本")
            return results
            
        except Exception as e:
            logger.error(f"获取随机样本失败: {e}")
            raise
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """
        获取集合统计信息
        
        Returns:
            集合统计信息
        """
        if not self.collection:
            raise ValueError("请先调用 connect_milvus() 连接数据库")
        
        try:
            stats = {
                "collection_name": self.collection_name,
                "total_entities": self.collection.num_entities,
                "schema": self.collection.schema,
                "indexes": self.collection.indexes
            }
            
            logger.info(f"集合统计信息: 总数据量 {stats['total_entities']}")
            return stats
            
        except Exception as e:
            logger.error(f"获取集合统计信息失败: {e}")
            raise

def main():
    """
    演示查询功能的使用方法
    """
    # 创建查询器
    query_engine = BrandKnowledgeQuery()
    
    try:
        # 连接数据库
        query_engine.connect_milvus()
        
        # 获取集合统计信息
        print("\n=== 集合统计信息 ===")
        stats = query_engine.get_collection_stats()
        print(f"集合名称: {stats['collection_name']}")
        print(f"总数据量: {stats['total_entities']}")
        
        # 随机获取一些样本
        print("\n=== 随机样本 ===")
        samples = query_engine.get_random_samples(3)
        for i, sample in enumerate(samples):
            print(f"{i+1}. [ID: {sample['id']}] {sample['text'][:100]}...")
        
        # 语义搜索示例
        print("\n=== 语义搜索示例 ===")
        query_text = "奔驰车型价格"
        search_results = query_engine.semantic_search(query_text, top_k=3)
        print(f"查询: {query_text}")
        for i, result in enumerate(search_results):
            print(f"{i+1}. [相似度: {result['score']:.4f}] {result['text'][:100]}...")
        
        # 关键词搜索示例
        print("\n=== 关键词搜索示例 ===")
        keyword = "BMW"
        keyword_results = query_engine.keyword_search(keyword, top_k=3)
        print(f"关键词: {keyword}")
        for i, result in enumerate(keyword_results):
            print(f"{i+1}. [ID: {result['id']}] {result['text'][:100]}...")
        
    except Exception as e:
        logger.error(f"查询演示失败: {e}")

if __name__ == "__main__":
    main()
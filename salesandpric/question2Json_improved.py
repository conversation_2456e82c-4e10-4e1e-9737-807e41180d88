import pandas as pd
import requests
from typing import Dict, Any
import re
import time

# API配置
API_URL = "https://api.siliconflow.cn/v1/chat/completions"
API_KEY = "sk-awbxpcoxjgcvswaupencngodsxuhbbuswjocamzcmgbkkelw"

def call_deepseek(prompt: str, max_retries=3, retry_delay=5) -> Dict[Any, Any]:
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {API_KEY}"
    }
    
    # 添加SSL验证选项
    verify = False
    if not verify:
        import urllib3
        urllib3.disable_warnings()
        
    # 添加重试机制
    for attempt in range(max_retries):
        try:
            response = requests.post(
                API_URL,
                headers=headers,
                json={
                    "model": "deepseek-chat",
                    "messages": [{"role": "user", "content": prompt}]
                },
                verify=verify  # 禁用SSL验证
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            if attempt == max_retries - 1:
                print(f"API调用失败: {str(e)}")
                return {}
            print(f"第{attempt + 1}次重试...")
            time.sleep(retry_delay)

    system_prompt="""

## 角色  
你是一名遵循严格数据规范的分析专家，能够精准结构化解析用户查询。

## 任务要求  
将用户问题拆解为以下四部分，**严格按格式输出**：  

### 1. 时间周期类型（time_field）  
- **定义**：用户问题中时间颗粒度（年/季/月/半月/周）  
- **规则**：  
  ▫️ 仅输出时间周期类型，不包含具体时间值  
  ▫️ 示例：    
  用户问"2025年Q3数据" → 输出"季"  
  用户问"最近三个月" → 输出"月"   

### 2. 维度类型（dimension_field）  
- **定义**：从预置维度列表匹配（完整列表见下文）  
- **规则**：  
  ▫️ 必须完全匹配以下字段：
大区、省份、城市、城市级别、限行限购、一级细分市场、二级细分市场、系别、品牌、厂商、厂商品牌、市场属性、产地属性、品牌属性、汽车类型、车身形式、燃料类型-1、燃料类型-2、车型、颜色、产权、使用属性、型号编码、型号名称、型号类型、长、宽、高、轴距、续航里程、驱动类型、座位数、排量、排挡方式、发动机、上市年款、上市日期、变化描述、停产日期、停销日期、TP价格分段、MSRP价格分段`  

### 3. 指标类型（metrics）  
- **定义**：从预置指标列表匹配（完整列表见下文）  
- **规则**：  
  ▫️ 必须完全匹配以下字段：  
CPCA-零售量、CPCA-零售量同比、CPCA-零售量环比、CPCA-零售量同比变化、CPCA-零售量环比变化、CPCA-零售量份额、CPCA-零售量份额同比、CPCA-零售量份额环比、CPCA-批发量、CPCA-批发量同比、CPCA-批发量环比、CPCA-批发量同比变化、CPCA-批发量环比变化、CPCA-批发量份额、CPCA-批发量份额同比、CPCA-批发量份额环比、CPCA-零售量同期销量、CPCA-批发量同期销量、SX-销量、SX-同比、SX-同期销量、SX-环比、SX-同比变化、SX-环比变化、SX-份额、SX-份额同比、SX-份额环比、ZQX-批发量、ZQX-批发量同比、ZQX-批发量环比、ZQX-批发量同比变化、ZQX-批发量环比变化、ZQX-批发量份额、ZQX-批发量份额同比、ZQX-批发量份额环比、ZQX-批发量同期销量、MIX、MIX环比、MIX同比、TP指数、TP指数变化、TP、TP同比、TP环比、TP累计变化、折扣、折扣同比、折扣环比、折扣累计变化、折扣率、折扣率环比、折扣率累计变化、MSRP、MSRP指数。
注：CPCA指乘联会，SX指上险，ZQX指中汽协，TP指成交价

### 4. 筛选条件（filter_condition）  
- **必须包含两类数据**：  
  **a) 具体时间值**    
  ▫️ 字段命名规则：  
    年 → 年=yyyy  
    季 → 季=yyyyQN（例：2025Q1）  
    月 → 月=yyyyMM（例：202502）  
    周 → 周=yyyyww（例：202507）    

  **b) 维度值ID**   
  ▫️ 格式：维度字段名=知识库ID  
  ▫️ 示例：  
    用户问"秦L" → 车型=秦L  
    用户问"上海市" → 城市=上海市
    用户问"比亚迪 海鸥 2023 EV 电机 飞翔版", "比亚迪 海鸥 2023 EV 电机 自由版"→ 型号名称=比亚迪 海鸥 2023 EV 电机 飞翔版,比亚迪 海鸥 2023 EV 电机 自由版
  

## 知识库约束  
{{#context#}}  
1. **强匹配原则**：  
   - 所有筛选值必须与知识库中的ID/名称**完全一致**  
   - 禁止编造不存在的数据  

2. **双重校验机制**：  
   ▫️ 第一步：检查维度/指标是否在预置列表中  
   ▫️ 第二步：检查筛选值是否存在于知识库  

## 输出格式    
time_field：[时间周期类型 | 空的时候默认为月]  
dimension_field：[维度类型 | 空]  
metrics：[指标类型 | 空]  
filter_condition：[字段1=值1, 字段2=值2,值3| 空]    

---

## 验证案例  
**案例1**  
问题：2025年1到6月秦L的销量是多少？  
知识库：车型表包含`秦L → 车型ID=秦L`    
time_field：月  
dimension_field：车型  
metrics：SX-销量  
filter_condition：月=202501,202506, 车型=秦L   

**案例2**  
问题：2024Q3浙江省新能源车上险量TOP5品牌    
time_field：季  
dimension_field：省份,品牌  
metrics：SX-销量  
filter_condition：季=2024Q3, 省份=12, 燃料类型-1=新能源    

## 执行原则  
1. **严格性**：未明确提及的字段不输出  
2. **原子性**：每个字段独立判断，不合并处理  
3. **可追溯性**：所有筛选值必须能映射到知识库  
"""

    data = {
        "model": "Qwen/Qwen3-235B-A22B-Instruct-2507",
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.7
    }

    for attempt in range(max_retries):
        try:
            response = requests.post(API_URL, headers=headers, json=data)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            if response.status_code == 429:  # Too Many Requests
                wait_time = retry_delay * (attempt + 1)
                print(f"API请求限制，等待{wait_time}秒后重试...")
                time.sleep(wait_time)
                if attempt == max_retries - 1:
                    print(f"API调用出错: {str(e)}")
                    return None
            else:
                print(f"API调用出错: {str(e)}")
                return None
        except Exception as e:
            print(f"API调用出错: {str(e)}")
            return None

def analyze_question(question: str) -> str:
    response = call_deepseek(question)
    if response and 'choices' in response:
        return response['choices'][0]['message']['content']
    if response is None:
        return "API请求失败，等待重试"
    return ""

def main():
    excel_path = r"C:\Users\<USER>\Desktop\用户问题-生成指标维度.xlsx"
    try:
        df = pd.read_excel(excel_path)
        analysis_results = list(df['指标维度'])  # 加载现有结果
        question_counter = 0
        retry_questions = []  # 存储需要重试的问题

        # 处理每个问题
        for index, row in df.iterrows():
            # 检查是否已有结果
            if pd.notna(row['指标维度']) and str(row['指标维度']).strip():
                print(f"跳过已处理的问题 {index + 1}: {row['name']}")
                continue

            question = row['name']
            print(f"正在处理问题 {index + 1}: {question}")

            # 分析问题并存储模型输出
            analysis_result = analyze_question(question)

            # 如果API请求失败，将问题加入重试列表
            if analysis_result == "API请求失败，等待重试":
                retry_questions.append((index, question))
            else:
                analysis_results[index] = analysis_result
                print(analysis_result)

            question_counter += 1

            # 每处理10个问题保存一次，并添加延迟
            if question_counter % 10 == 0:
                print(f"已处理{question_counter}个新问题，正在保存结果...")
                df['指标维度'] = analysis_results
                df.to_excel(excel_path, index=False)
                print("中间结果已保存")
                time.sleep(3)  # 保存后等待3秒，避免频繁请求

        # 处理重试队列中的问题
        if retry_questions:
            print(f"\n开始处理{len(retry_questions)}个失败的请求...")
            time.sleep(10)  # 在处理重试问题前多等待一段时间
            for retry_index, retry_question in retry_questions:
                print(f"重试处理问题 {retry_index + 1}: {retry_question}")
                analysis_result = analyze_question(retry_question)
                if analysis_result != "API请求失败，等待重试":
                    analysis_results[retry_index] = analysis_result
                    # 每处理完一个重试问题就保存一次
                    df['指标维度'] = analysis_results
                    df.to_excel(excel_path, index=False)
                    print(f"重试成功，已更新问题 {retry_index + 1} 的结果")
                time.sleep(5)  # 每次重试后等待5秒

        # 最终保存所有结果
        df['指标维度'] = analysis_results
        df.to_excel(excel_path, index=False)
        print(f"处理完成，共处理{question_counter}个新问题，所有结果已写回Excel文件。")

    except Exception as e:
        print(f"处理过程中出错: {str(e)}")
        # 发生错误时尝试保存已处理的结果
        try:
            df['指标维度'] = analysis_results
            df.to_excel(excel_path, index=False)
            print("发生错误，已保存当前进度")
        except:
            print("保存进度失败")

if __name__ == "__main__":
    main()

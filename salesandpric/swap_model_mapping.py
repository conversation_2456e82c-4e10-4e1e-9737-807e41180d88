import json

def swap_key_value():
    # 读取原始JSON文件
    try:
        with open('model.json', 'r', encoding='utf-8') as f:
            original_data = json.load(f)
    except FileNotFoundError:
        print("错误：找不到model.json文件")
        return
    except json.JSONDecodeError:
        print("错误：model.json文件格式不正确")
        return
    
    # 创建新的字典，交换key和value
    swapped_data = {}
    for key, value in original_data.items():
        # 检查是否有重复的车型名称
        if value in swapped_data:
            print(f"警告：发现重复的车型名称 '{value}'，原ID: {swapped_data[value]}，新ID: {key}")
            # 可以选择保留新的或旧的映射，这里选择保留新的
        swapped_data[value] = key
    
    # 将新的映射写入文件
    try:
        with open('model_swapped.json', 'w', encoding='utf-8') as f:
            json.dump(swapped_data, f, ensure_ascii=False, indent=2)
        print("转换完成！新文件已保存为 model_swapped.json")
    except Exception as e:
        print(f"错误：保存文件时出错 - {str(e)}")

if __name__ == "__main__":
    swap_key_value()
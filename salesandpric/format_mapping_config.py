#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将mapping_config-new.json中的数据格式化并写入mapping_config.json

作者: AI助手
创建时间: 2025年1月25日
功能: 
1. 读取mapping_config-new.json文件
2. 提取mapping_config和id_mapping_config数据
3. 按照标准格式重新组织数据
4. 写入mapping_config.json文件
"""

import json
import os
from typing import Dict, Any
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class MappingConfigFormatter:
    """映射配置格式化器"""
    
    def __init__(self, input_file: str = "mapping_config-new.json", output_file: str = "mapping_config.json"):
        """
        初始化格式化器
        
        Args:
            input_file: 输入文件路径
            output_file: 输出文件路径
        """
        self.input_file = input_file
        self.output_file = output_file
        
    def load_source_data(self) -> Dict[str, Any]:
        """
        加载源数据文件
        
        Returns:
            源数据字典
        """
        try:
            if not os.path.exists(self.input_file):
                raise FileNotFoundError(f"源文件不存在: {self.input_file}")
            
            with open(self.input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            logger.info(f"成功加载源数据文件: {self.input_file}")
            return data
        except Exception as e:
            logger.error(f"加载源数据文件失败: {e}")
            raise
    
    def format_mapping_config(self, source_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化映射配置数据
        
        Args:
            source_data: 源数据字典
            
        Returns:
            格式化后的数据字典
        """
        try:
            # 提取mapping_config和id_mapping_config
            mapping_config = source_data.get("mapping_config", {})
            id_mapping_config = source_data.get("id_mapping_config", {})
            
            # 构建输出数据结构
            formatted_data = {
                "mapping_config": mapping_config,
                "id_mapping_config": id_mapping_config
            }
            
            # 统计信息
            mapping_categories = len(mapping_config)
            id_mapping_categories = len(id_mapping_config)
            
            total_mapping_items = sum(len(category) for category in mapping_config.values())
            total_id_mapping_items = sum(len(category) for category in id_mapping_config.values())
            
            logger.info(f"格式化完成:")
            logger.info(f"  mapping_config: {mapping_categories} 个类别, {total_mapping_items} 个映射项")
            logger.info(f"  id_mapping_config: {id_mapping_categories} 个类别, {total_id_mapping_items} 个映射项")
            
            return formatted_data
            
        except Exception as e:
            logger.error(f"格式化数据失败: {e}")
            raise
    
    def save_formatted_data(self, formatted_data: Dict[str, Any]) -> None:
        """
        保存格式化后的数据
        
        Args:
            formatted_data: 格式化后的数据字典
        """
        try:
            # 创建备份文件（如果输出文件已存在）
            if os.path.exists(self.output_file):
                backup_file = f"{self.output_file}.backup"
                with open(self.output_file, 'r', encoding='utf-8') as src, \
                     open(backup_file, 'w', encoding='utf-8') as dst:
                    dst.write(src.read())
                logger.info(f"已创建备份文件: {backup_file}")
            
            # 写入格式化后的数据
            with open(self.output_file, 'w', encoding='utf-8') as f:
                json.dump(formatted_data, f, ensure_ascii=False, indent=2, separators=(',', ': '))
            
            logger.info(f"成功写入输出文件: {self.output_file}")
            
        except Exception as e:
            logger.error(f"保存格式化数据失败: {e}")
            raise
    
    def validate_output(self) -> bool:
        """
        验证输出文件的有效性
        
        Returns:
            验证是否通过
        """
        try:
            with open(self.output_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 检查必要的键
            required_keys = ["mapping_config", "id_mapping_config"]
            for key in required_keys:
                if key not in data:
                    logger.error(f"输出文件缺少必要的键: {key}")
                    return False
            
            # 检查数据类型
            if not isinstance(data["mapping_config"], dict):
                logger.error("mapping_config 应该是字典类型")
                return False
                
            if not isinstance(data["id_mapping_config"], dict):
                logger.error("id_mapping_config 应该是字典类型")
                return False
            
            logger.info("输出文件验证通过")
            return True
            
        except Exception as e:
            logger.error(f"验证输出文件失败: {e}")
            return False
    
    def run(self) -> bool:
        """
        运行格式化流程
        
        Returns:
            是否成功完成
        """
        try:
            logger.info("开始格式化映射配置...")
            
            # 1. 加载源数据
            source_data = self.load_source_data()
            
            # 2. 格式化数据
            formatted_data = self.format_mapping_config(source_data)
            
            # 3. 保存格式化后的数据
            self.save_formatted_data(formatted_data)
            
            # 4. 验证输出
            if not self.validate_output():
                logger.error("输出文件验证失败")
                return False
            
            logger.info("映射配置格式化完成!")
            return True
            
        except Exception as e:
            logger.error(f"格式化流程失败: {e}")
            return False


def main():
    """主函数"""
    try:
        # 创建格式化器实例
        formatter = MappingConfigFormatter()
        
        # 运行格式化流程
        success = formatter.run()
        
        if success:
            print("✅ 映射配置格式化成功完成!")
            print(f"📁 输出文件: {formatter.output_file}")
        else:
            print("❌ 映射配置格式化失败!")
            exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        exit(1)
    except Exception as e:
        print(f"❌ 发生未知错误: {e}")
        exit(1)


if __name__ == "__main__":
    main()
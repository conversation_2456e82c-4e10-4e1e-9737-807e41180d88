import pandas as pd
import requests
from typing import Dict, Any
import re

from langchain_experimental.graph_transformers.llm import system_prompt

# API配置
API_URL = "https://api.siliconflow.cn/v1/chat/completions"
API_KEY = "sk-awbxpcoxjgcvswaupencngodsxuhbbuswjocamzcmgbkkelw"

def call_deepseek(prompt: str) -> Dict[Any, Any]:
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {API_KEY}"
    }

    system_prompt="""
## 角色  
你是一名遵循严格数据规范的分析专家，能够精准结构化解析用户查询。

## 任务要求  
将用户问题拆解为以下四部分，**严格按格式输出**：  

### 1. 时间周期类型（time_field）  
- **定义**：用户问题中时间颗粒度（年/季/月/半月/周）  
- **规则**：  
  ▫️ 仅输出时间周期类型，不包含具体时间值  
  ▫️ 示例：    
  用户问"2025年Q3数据" → 输出"季"  
  用户问"最近三个月" → 输出"月"  
 

### 2. 维度类型（dimension_field）  
- **定义**：从预置维度列表匹配（完整列表见下文）  
- **规则**：  
  ▫️ 必须完全匹配以下字段：
大区、省份、城市、城市级别、限行限购、一级细分市场、二级细分市场、系别、品牌、厂商、厂商品牌、市场属性、产地属性、品牌属性、汽车类型、车身形式、燃料类型-1、燃料类型-2、车型、颜色、产权、使用属性、型号编码、型号名称、型号类型、长、宽、高、轴距、续航里程、驱动类型、座位数、排量、排挡方式、发动机、上市年款、上市日期、变化描述、停产日期、停销日期、TP价格分段、MSRP价格分段`  

### 3. 指标类���（metrics）  
- **定义**：从预置指标列表匹配（完整列表见下文）  
- **规则**：  
  ▫️ 必须完全匹配以下字段：  
CPCA-零售量、CPCA-零售量同比、CPCA-零售量环比、CPCA-零售量同比变化、CPCA-零售量环比变化、CPCA-零售量份额、CPCA-零售量份额同比、CPCA-零售量份额环比、CPCA-批发量、CPCA-批发量同比、CPCA-批发量环比、CPCA-批发量同比变化、CPCA-批发量环比变化、CPCA-批发量份额、CPCA-批发量份额同比、CPCA-批发量份额环比、CPCA-零售量同期销量、CPCA-批发量同期销量、SX-销量、SX-同比、SX-同期销量、SX-环比、SX-同比变化、SX-环比变化、SX-份额、SX-份额同比、SX-份额环比、ZQX-批发量、ZQX-批发量同比、ZQX-批发量环比、ZQX-批发量同比变化、ZQX-批发量环比变化、ZQX-批发量份额、ZQX-批发量份额同比、ZQX-批发量份额环比、ZQX-批发量同期销量、MIX、MIX环比、MIX同比、TP指数、TP指数变化、TP、TP同比、TP环比、TP累计变化、折扣、折扣同比、折扣环比、折扣累计变化、折扣率、折扣率环比、折扣率累计变化、MSRP、MSRP指数。
注：CPCA指乘联会，SX指上险，ZQX指中汽协，TP指成交价

### 4. 筛选条件（filter_condition）  
- **必须包含两类数据**：  
  **a) 具体时间值**    
  ▫️ 字段命名规则：  
    年 → 年=yyyy  
    季 → 季=yyyyQN（例：2025Q1）  
    月 → 月=yyyyMM（例：202502）  
    周 → 周=yyyyww（例：202507）    

  **b) 维度值ID**    
  ▫️ 格式：维度字段名=知识库ID  
  ▫️ 示例：  
    用户问"秦L" → 车型=秦L  
    用户问"上海市" → 城市=上海市
    用户问"比亚迪 海鸥 2023 EV 电机 飞翔版", "比亚迪 海鸥 2023 EV 电机 自由版"→ 型号名称=534060,534059
  

## 知识库约束  
{{#context#}}  
1. **强匹配原则**：  
   - 所有筛选值必须与知识库中的ID/名称**完全一致**  
   - 禁止编造不存在的数据  

2. **双重校验机制**：  
   ▫️ 第一步：检查维度/指标是否在预置列表中  
   ▫️ 第二步：检查筛选值是否存在于知识库

3. **参考资料**：
   - 【品牌属性】包括的属性值：豪华、非豪华
   - 【市场属性】包括的属性值：豪华、自主、合资、新势力
   - 【厂商品牌属性】包括的属性值：合资、合资自主、进口、外商独资、自主
   - 【系别】包括的属性值：德系、法系、韩系、美系、欧系、日系、自主
   - 【一级细分市场】包括的属性值：A00、A0、A、B、C、D、A-MPV、B-MPV、A-SUV、B-SUV、A0-SUV、C-SUV、D-SUV、跑车
   - 【二级细分市场】包括的属性值：A00、A0-L、A0-H、A-L、A-M、A-H、A-S、B-L、B-M、B-H、C-L、C-H、D、A0-SUV-L、A0-SUV-H、A-SUV-L、A-SUV-M、A-SUV-H、B-SUV-L、B-SUV-M、B-SUV-H、C-SUV-L、C-SUV-H、D-SUV、A-MPV
   - 【燃料类型-1】包括的属性值：新能源、传统能源
   - 【燃料类型-2】包括的属性值：纯电动、插电式混合动力、增程型电动、燃料电池、柴油、非插电式混合动力、汽油、双燃料、非插电式轻混合动力、非插电式增程型电动

## 输出格式  
time_field：[时间周期类型 | 空的时候默认为月]  
dimension_field：[维度类型 | 空]  
metrics：[指标类型 | 空]  
filter_condition：[字段1=值1, 字段2=值2,值3| 空]  

## 验证案例  
**案例1**  
问题：2025年1到6月秦L的销量是多少？  
知识库：车型表包含`秦L → 车型ID=秦L`    
time_field：月  
dimension_field：车型  
metrics：SX-销量  
filter_condition：月=202501,202506, 车型=秦L    

**案例2**  
问题：2024Q3浙江省新能源车上险量TOP5品牌    
time_field：季  
dimension_field：省份,品牌  
metrics：SX-销量  
filter_condition：季=2024Q3, 省份=12, 燃料类型-1=新能源

**案例3**  
问题：统计维度：年、车型、SX销量，筛选条件：月份为25年1到6月，省份为湖北省？  
知识库：车型表包含`秦L → 车型ID=秦L`    
time_field：年  
dimension_field：车型  
metrics：SX-销量  
filter_condition：月=202501,202506, 省份=湖北省

## 执行原则  
1. **严格性**：未明确提及的字段不输出  
2. **原子性**：每个字段独立判断，不合并处理  
3. **可追溯性**：所有筛选值必须能映射到知识库

注意：当前时间2025年8月 
    """

    data = {
        "model": "Pro/deepseek-ai/DeepSeek-V3", # Qwen/Qwen3-235B-A22B-Instruct-2507
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.7
    }

    try:
        response = requests.post(API_URL, headers=headers, json=data)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"API调用出错: {str(e)}")
        return None

def analyze_question(question: str) -> str:
    response = call_deepseek(question)
    if response and 'choices' in response:
        return response['choices'][0]['message']['content']
    return ""

def main():
    # 读取Excel文件
    excel_path = r"C:\Users\<USER>\Desktop\用户问题-生成指标维度.xlsx"
    try:
        df = pd.read_excel(excel_path)

        # 存储分析结果
        last_json_result = []
        analysis_results = []
        query_filters = []

        # 添加计数器
        question_counter = 0

        # 处理每个问题
        for index, row in df.iterrows():
            question = row['name']
            print(f"正在处理问题 {index + 1}: {question}")

            # 分析问题并直接存储模型输出
            analysis_result = analyze_question(question)

            # 存储结果
            analysis_results.append(analysis_result)
            print(analysis_result)

            # 增加计数器
            question_counter += 1

            # 每处理10个问题保存一次
            if question_counter % 10 == 0:
                print(f"已处理{question_counter}个问题，正在保存结果...")
                # 更新DataFrame
                df.loc[:index, '指标维度'] = analysis_results
                # 保存到Excel
                df.to_excel(excel_path, index=False)
                print("中间结果已保存")

        # 处理完所有问题后，保存剩余结果
        df['指标维度'] = analysis_results
        df.to_excel(excel_path, index=False)
        print(f"处理完成，共处理{question_counter}个问题，所有结果已写回Excel文件。")

    except Exception as e:
        print(f"处理过程中出错: {str(e)}")


def query_to_json(input_data):
    field_mapping = {
        '年': {'列类型编码': '1', '列名称': 'BASE_YY_NAME', '列编码': 'BASE_YY', '列属性': 'dm_group_id'},
        '季': {'列类型编码': '1', '列名称': 'BASE_YQ_NAME', '列编码': 'BASE_YQ', '列属性': 'dm_group_id'},
        '月': {'列类型编码': '1', '列名称': 'BASE_YM_NAME', '列编码': 'BASE_YM', '列属性': 'dm_group_id'},
        '半月': {'列类型编码': '1', '列名称': 'BASE_YMH_NAME', '列编码': 'BASE_YMH', '列属性': 'dm_group_id'},
        '周': {'列类型编码': '1', '列名称': 'BASE_YMW_NAME', '列编码': 'BASE_YMW', '列属性': 'dm_group_id'},
        '大区': {'列类型编码': '2', '列名称': 'REGION_NAME', '列编码': 'REGION_ID', '列属性': 'dm_group_id'},
        '省份': {'列类型编码': '2', '列名称': 'PROVINCE_NAME', '列编码': 'PROVINCE_ID', '列属性': 'dm_group_id'},
        '城市': {'列类型编码': '2', '列名称': 'CITY_NAME', '列编码': 'CITY_ID', '列属性': 'dm_group_id'},
        '城市级别': {'列类型编码': '2', '列名称': 'CITY_LEVEL_NAME', '列编码': 'CITY_LEVEL_ID','列属性': 'dm_group_id'},
        '限行限购': {'列类型编码': '2', '列名称': 'LIMIT_TYPE_NAME', '列编码': 'LIMIT_TYPE_CODE','列属性': 'dm_group_id'},
        '一级细分市场': {'列类型编码': '3', '列名称': 'SEGMENT_LV1_NAME', '列编码': 'SEGMENT_LV1_ID','列属性': 'dm_group_id'},
        '二级细分市场': {'列类型编码': '3', '列名称': 'SEGMENT_LV2_NAME', '列编码': 'SEGMENT_LV2_ID','列属性': 'dm_group_id'},
        '三级细分市场': {'列类型编码': '3', '列名称': 'SEGMENT_LV3_NAME', '列编码': 'SEGMENT_LV3_ID','列属性': 'dm_group_id'},
        '四级细分市场': {'列类型编码': '3', '列名称': 'SEGMENT_LV4_NAME', '列编码': 'SEGMENT_LV4_ID','列属性': 'dm_group_id'},
        '五级细分市场': {'列类型编码': '3', '列名称': 'SEGMENT_LV5_NAME', '列编码': 'SEGMENT_LV5_ID','列属性': 'dm_group_id'},
        '系别': {'列类型编码': '3', '列名称': 'BRAND_NATI_NAME', '列编码': 'BRAND_NATI_ID', '列属性': 'dm_group_id'},
        '品牌': {'列类型编码': '3', '列名称': 'BRAND_NAME', '列编码': 'BRAND_ID', '列属性': 'dm_group_id'},
        '厂商': {'列类型编码': '3', '列名称': 'MANF_NAME', '列编码': 'MANF_ID', '列属性': 'dm_group_id'},
        '厂商品牌': {'列类型编码': '3', '列名称': 'MANF_BRAND_NAME', '列编码': 'MANF_BRAND_ID','列属性': 'dm_group_id'},
        '市场属性': {'列类型编码': '3', '列名称': 'MARKET_PROP_NAME', '列编码': 'MARKET_PROP_ID','列属性': 'dm_group_id'},
        '产地属性': {'列类型编码': '3', '列名称': 'MANF_BRAND_PROP_NAME', '列编码': 'MANF_BRAND_PROP_ID','列属性': 'dm_group_id'},
        '品牌属性': {'列类型编码': '3', '列名称': 'BRAND_PROP_NAME', '列编码': 'BRAND_PROP_ID','列属性': 'dm_group_id'},
        '汽车类型': {'列类型编码': '3', '列名称': 'VEHICLE_TYPE_NAME', '列编码': 'VEHICLE_TYPE_ID','列属性': 'dm_group_id'},
        '车身形式': {'列类型编码': '3', '列名称': 'BODY_TYPE_NAME', '列编码': 'BODY_TYPE_ID', '列属性': 'dm_group_id'},
        '车型': {'列类型编码': '3', '列名称': 'SUB_MODEL_NAME', '列编码': 'SUB_MODEL_ID', '列属性': 'dm_group_id'},
        '型号编码': {'列类型编码': '4', '列名称': 'VERSION_CODE_NAME', '列编码': 'VERSION_CODE','列属性': 'dm_group_id'},
        '型号名称': {'列类型编码': '4', '列名称': 'VERSION_FULL_NAME', '列编码': 'VERSION_ID', '列属性': 'dm_group_id'},
        '燃料类型-1': {'列类型编码': '4', '列名称': 'FUEL_DIV_NAME', '列编码': 'FUEL_DIV_ID', '列属性': 'dm_group_id'},
        '燃料类型-2': {'列类型编码': '4', '列名称': 'FUEL_TYPE_NAME', '列编码': 'FUEL_TYPE_ID','列属性': 'dm_group_id'},
        '长': {'列类型编码': '4', '列名称': 'LENGTH_VAL_NAME', '列编码': 'LENGTH_VAL', '列属性': 'dm_group_id'},
        '宽': {'列类型编码': '4', '列名称': 'WIDTH_VAL_NAME', '列编码': 'WIDTH_VAL', '列属性': 'dm_group_id'},
        '高': {'列类型编码': '4', '列名称': 'HEIGTH_VAL_NAME', '列编码': 'HEIGTH_VAL', '列属性': 'dm_group_id'},
        '轴距': {'列类型编码': '4', '列名称': 'WHEELBASE_VAL_NAME', '列编码': 'WHEELBASE_VAL', '列属性': 'dm_group_id'},
        '续航里程': {'列类型编码': '4', '列名称': 'RANGE_VAL_NAME', '列编码': 'RANGE_VAL', '列属性': 'dm_group_id'},
        '驱动类型': {'列类型编码': '4', '列名称': 'DRIVING_TYPE_NAME', '列编码': 'DRIVING_TYPE_ID','列属性': 'dm_group_id'},
        '座位数': {'列类型编码': '4', '列名称': 'SEAT_NAME', '列编码': 'SEAT_ID', '列属性': 'dm_group_id'},
        '排量': {'列类型编码': '4', '列名称': 'DISPLACEMENT_NAME', '列编码': 'DISPLACEMENT_ID','列属性': 'dm_group_id'},
        '排挡方式': {'列类型编码': '4', '列名称': 'TRAN_TYPE_NAME', '列编码': 'TRAN_TYPE_ID', '列属性': 'dm_group_id'},
        '发动机': {'列类型编码': '4', '列名称': 'ENGINE_TYPE_NAME', '列编码': 'ENGINE_TYPE_ID','列属性': 'dm_group_id'},
        '上市年款': {'列类型编码': '4', '列名称': 'MODEL_YEAR_NAME', '列编码': 'MODEL_YEAR', '列属性': 'dm_group_id'},
        '上市日期': {'列类型编码': '4', '列名称': 'LAUNCH_DATE_NAME', '列编码': 'LAUNCH_DATE', '列属性': 'dm_group_id'},
        '变化描述': {'列类型编码': '4', '列名称': 'CHANGES_NAME', '列编码': 'CHANGES_ID', '列属性': 'dm_group_id'},
        '停产日期': {'列类型编码': '4', '列名称': 'HALT_PRODUCT_DATE_NAME', '列编码': 'HALT_PRODUCT_DATE','列属性': 'dm_group_id'},
        '停销日期': {'列类型编码': '4', '列名称': 'HALT_SALE_DATE_NAME', '列编码': 'HALT_SALE_DATE','列属性': 'dm_group_id'},
        '颜色': {'列类型编码': '4', '列名称': 'BODY_COLOR_NAME', '列编码': 'BODY_COLOR_ID', '列属性': 'dm_group_id'},
        '产权': {'列类型编码': '4', '列名称': 'OWNERSHIP_NAME', '列编码': 'OWNERSHIP_ID', '列属性': 'dm_group_id'},
        '使用属性': {'列类型编码': '4', '列名称': 'USE_PROP_NAME', '列编码': 'USE_PROP_ID', '列属性': 'dm_group_id'},
        '型号类型': {'列类型编码': '4', '列名称': 'VERSION_TYPE_NAME', '列编码': 'VERSION_TYPE_ID','列属性': 'dm_group_id'},
        'CPCA-零售量': {'列类型编码': '1', '列名称': 'CPCA_SALE_QTY', '列编码': 'CPCA_SALE_QTY','列属性': 'data_source_id'},
        'CPCA-零售量同比': {'列类型编码': '1', '列名称': 'CPCA_SALE_YOY_RATE', '列编码': 'CPCA_SALE_YOY_RATE','列属性': 'data_source_id'},
        'CPCA-零售量环比': {'列类型编码': '1', '列名称': 'CPCA_SALE_MOM_RATE', '列编码': 'CPCA_SALE_MOM_RATE','列属性': 'data_source_id'},
        'CPCA-零售量同比变化': {'列类型编码': '1', '列名称': 'CPCA_SALE_YOY_VAR', '列编码': 'CPCA_SALE_YOY_VAR','列属性': 'data_source_id'},
        'CPCA-零售量环比变化': {'列类型编码': '1', '列名称': 'CPCA_SALE_MOM_VAR', '列编码': 'CPCA_SALE_MOM_VAR','列属性': 'data_source_id'},
        'CPCA-零售量份额': {'列类型编码': '1', '列名称': 'CPCA_SALE_SHARE_RATE', '列编码': 'CPCA_SALE_SHARE_RATE','列属性': 'data_source_id'},
        'CPCA-零售量份额同比': {'列类型编码': '1', '列名称': 'CPCA_SALE_YOY_SHARE_RATE','列编码': 'CPCA_SALE_YOY_SHARE_RATE', '列属性': 'data_source_id'},
        'CPCA-零售量份额环比': {'列类型编码': '1', '列名称': 'CPCA_SALE_MOM_SHARE_RATE','列编码': 'CPCA_SALE_MOM_SHARE_RATE', '列属性': 'data_source_id'},
        'CPCA-批发量': {'列类型编码': '2', '列名称': 'CPCA_WS_QTY', '列编码': 'CPCA_WS_QTY','列属性': 'data_source_id'},
        'CPCA-批发量同比': {'列类型编码': '2', '列名称': 'CPCA_WS_YOY_RATE', '列编码': 'CPCA_WS_YOY_RATE','列属性': 'data_source_id'},
        'CPCA-批发量环比': {'列类型编码': '2', '列名称': 'CPCA_WS_MOM_RATE', '列编码': 'CPCA_WS_MOM_RATE','列属性': 'data_source_id'},
        'CPCA-批发量同比变化': {'列类型编码': '2', '列名称': 'CPCA_WS_YOY_VAR', '列编码': 'CPCA_WS_YOY_VAR','列属性': 'data_source_id'},
        'CPCA-批发量环比变化': {'列类型编码': '2', '列名称': 'CPCA_WS_MOM_VAR', '列编码': 'CPCA_WS_MOM_VAR','列属性': 'data_source_id'},
        'CPCA-批发量份额': {'列类型编码': '2', '列名称': 'CPCA_WS_SHARE_RATE', '列编码': 'CPCA_WS_SHARE_RATE','列属性': 'data_source_id'},
        'CPCA-批发量份额同比': {'列类型编码': '2', '列名称': 'CPCA_WS_YOY_SHARE_RATE',
                                '列编码': 'CPCA_WS_YOY_SHARE_RATE', '列属性': 'data_source_id'},
        'CPCA-批发量份额环比': {'列类型编码': '2', '列名称': 'CPCA_WS_MOM_SHARE_RATE',
                                '列编码': 'CPCA_WS_MOM_SHARE_RATE', '列属性': 'data_source_id'},
        'CPCA-零售量同期销量': {'列类型编码': '2', '列名称': 'CPCA_SALE_YOY_QTY', '列编码': 'CPCA_SALE_YOY_QTY',
                                '列属性': 'data_source_id'},
        'CPCA-批发量同期销量': {'列类型编码': '2', '列名称': 'CPCA_WS_YOY_QTY', '列编码': 'CPCA_WS_YOY_QTY',
                                '列属性': 'data_source_id'},
        'SX-销量': {'列类型编码': '3', '列名称': 'SX_SALE_QTY', '列编码': 'SX_SALE_QTY', '列属性': 'data_source_id'},
        'SX-同比': {'列类型编码': '3', '列名称': 'SX_SALE_YOY_RATE', '列编码': 'SX_SALE_YOY_RATE',
                    '列属性': 'data_source_id'},
        'SX-同期销量': {'列类型编码': '3', '列名称': 'SX_SALE_YOY_QTY', '列编码': 'SX_SALE_YOY_QTY',
                        '列属性': 'data_source_id'},
        'SX-环比': {'列类型编码': '3', '列名称': 'SX_SALE_MOM_RATE', '列编码': 'SX_SALE_MOM_RATE',
                    '列属性': 'data_source_id'},
        'SX-同比变化': {'列类型编码': '3', '列名称': 'SX_SALE_YOY_VAR', '列编码': 'SX_SALE_YOY_VAR',
                        '列属性': 'data_source_id'},
        'SX-环比变化': {'列类型编码': '3', '列名称': 'SX_SALE_MOM_VAR', '列编码': 'SX_SALE_MOM_VAR',
                        '列属性': 'data_source_id'},
        'SX-份额': {'列类型编码': '3', '列名称': 'SX_SALE_SHARE_RATE', '列编码': 'SX_SALE_SHARE_RATE',
                    '列属性': 'data_source_id'},
        'SX-份额同比': {'列类型编码': '3', '列名称': 'SX_SALE_YOY_SHARE_RATE', '列编码': 'SX_SALE_YOY_SHARE_RATE',
                        '列属性': 'data_source_id'},
        'SX-份额环比': {'列类型编码': '3', '列名称': 'SX_SALE_MOM_SHARE_RATE', '列编码': 'SX_SALE_MOM_SHARE_RATE',
                        '列属性': 'data_source_id'},
        'ZQX-批发量': {'列类型编码': '4', '列名称': 'ZQX_WS_QTY', '列编码': 'ZQX_WS_QTY', '列属性': 'data_source_id'},
        'ZQX-批发量同比': {'列类型编码': '4', '列名称': 'ZQX_WS_YOY_RATE', '列编码': 'ZQX_WS_YOY_RATE',
                           '列属性': 'data_source_id'},
        'ZQX-批发量环比': {'列类型编码': '4', '列名称': 'ZQX_WS_MOM_RATE', '列编码': 'ZQX_WS_MOM_RATE',
                           '列属性': 'data_source_id'},
        'ZQX-批发量同比变化': {'列类型编码': '4', '列名称': 'ZQX_WS_YOY_VAR', '列编码': 'ZQX_WS_YOY_VAR',
                               '列属性': 'data_source_id'},
        'ZQX-批发量环比变化': {'列类型编码': '4', '列名称': 'ZQX_WS_MOM_VAR', '列编码': 'ZQX_WS_MOM_VAR',
                               '列属性': 'data_source_id'},
        'ZQX-批发量份额': {'列类型编码': '4', '列名称': 'ZQX_WS_SHARE_RATE', '列编码': 'ZQX_WS_SHARE_RATE',
                           '列属性': 'data_source_id'},
        'ZQX-批发量份额同比': {'列类型编码': '4', '列名称': 'ZQX_WS_YOY_SHARE_RATE', '列编码': 'ZQX_WS_YOY_SHARE_RATE',
                               '列属性': 'data_source_id'},
        'ZQX-批发量份额环比': {'列类型编码': '4', '列名称': 'ZQX_WS_MOM_SHARE_RATE', '列编码': 'ZQX_WS_MOM_SHARE_RATE',
                               '列属性': 'data_source_id'},
        'ZQX-批发量同期销量': {'列类型编码': '4', '列名称': 'ZQX_WS_YOY_QTY', '列编码': 'ZQX_WS_YOY_QTY',
                               '列属性': 'data_source_id'},
        'MIX': {'列类型编码': '5', '列名称': 'MIX', '列编码': 'MIX', '列属性': 'data_source_id'},
        'MIX环比': {'列类型编码': '5', '列名称': 'MIX_VAR', '列编码': 'MIX_VAR', '列属性': 'data_source_id'},
        'MIX同比': {'列类型编码': '3', '列名称': 'MIX_YOY', '列编码': 'MIX_YOY', '列属性': 'data_source_id'},
        'TP指数': {'列类型编码': '6', '列名称': 'TP_INDEX', '列编码': 'TP_INDEX', '列属性': 'data_source_id'},
        'TP指数变化': {'列类型编码': '6', '列名称': 'CHG_TP_INDEX', '列编码': 'CHG_TP_INDEX',
                       '列属性': 'data_source_id'},
        'TP': {'列类型编码': '6', '列名称': 'TP', '列编码': 'TP', '列属性': 'data_source_id'},
        'TP同比': {'列类型编码': '6', '列名称': 'YOY_TP', '列编码': 'YOY_TP', '列属性': 'data_source_id'},
        'TP环比': {'列类型编码': '6', '列名称': 'MOM_TP', '列编码': 'MOM_TP', '列属性': 'data_source_id'},
        'TP累计变化': {'列类型编码': '6', '列名称': 'ACCUCHG_TP', '列编码': 'ACCUCHG_TP', '列属性': 'data_source_id'},
        '折扣': {'列类型编码': '6', '列名称': 'DISCOUNT_VAL', '列编码': 'DISCOUNT_VAL', '列属性': 'data_source_id'},
        '折扣同比': {'列类型编码': '6', '列名称': 'YOY_DISCOUNT_VAL', '列编码': 'YOY_DISCOUNT_VAL',
                     '列属性': 'data_source_id'},
        '折扣环比': {'列类型编码': '6', '列名称': 'MOM_DISCOUNT_VAL', '列编码': 'MOM_DISCOUNT_VAL',
                     '列属性': 'data_source_id'},
        '折扣累计变化': {'列类型编码': '6', '列名称': 'ACCUCHG_DISCOUNT_VAL', '列编码': 'ACCUCHG_DISCOUNT_VAL','列属性': 'data_source_id'},
        '折扣率': {'列类型编码': '6', '列名称': 'DISCOUNT_RATE', '列编码': 'DISCOUNT_RATE', '列属性': 'data_source_id'},
        '折扣率环比': {'列类型编码': '6', '列名称': 'MOM_DISCOUNT_RATE', '列编码': 'MOM_DISCOUNT_RATE','列属性': 'data_source_id'},
        '折扣率累计变化': {'列类型编码': '6', '列名称': 'ACCUCHG_DISCOUNT_RATE', '列编码': 'ACCUCHG_DISCOUNT_RATE','列属性': 'data_source_id'},
        'MSRP': {'列类型编码': '6', '列名称': 'MSRP', '列编码': 'MSRP', '列属性': 'data_source_id'},
        'MSRP指数': {'列类型编码': '6', '列名称': 'MSRP_INDEX', '列编码': 'MSRP_INDEX', '列属性': 'data_source_id'}
    }

    # 提取 result 部分
    # result = inp.get("inp", {})

    # 提取 query 和 filters
    query_array = input_data.get("query", [])
    filter_condition = input_data.get("filters", {})

    # 构建dataColumn
    data_column = []
    for field in query_array:
        if field in field_mapping:
            info = field_mapping[field]

            column_entry = {
                "columnAttr": info['列属性'],
                "columnId": info['列编码'],
                "columnName": info['列名称'],
                "columnType": info['列类型编码'],
                "labelName": field
            }

            # 特殊处理 MIX、MIX环比、MIX同比
            if field in ['MIX', 'MIX环比', 'MIX同比']:
                column_entry["mixType"] = "1"
            data_column.append(column_entry)

    data_filtering = []
    for field, values in filter_condition.items():
        if field in field_mapping:
            info = field_mapping[field]
            # 确保 columnValues 是一个数组
            if not isinstance(values, list):
                values = [values]  # 将非列表值转换为列表
            data_filtering.append({
                "columnAttr": info['列属性'],
                "columnId": info['列编码'],
                "columnType": info['列类型编码'],
                "columnValues": values,
                "labelName": field
            })

    # 构建最终响应
    response = {
        "data": {
            "currentPage": 1,
            "dataColumn": data_column,
            "dataFiltering": data_filtering,
            "pageSize": 100
        },
        "token": "eyJhbGciOiJIUzUxMiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.yLGmyinaGbsqBcztNv9y6ispkGeOQcrHHxfcMPJGAi8rhwzTuugPzRAyxuO7pbJDHsq7RZnMYjKjnUM0MearBw"
    }

    return response


def format_output(input_string):
    # 初始化变量
    time_field = ""
    dimension_field = []
    metrics = []
    filter_condition_str = ""

    # 按行分割输入字符串
    lines = input_string.strip().split("\n")

    for line in lines:
        line = line.strip()
        if line.startswith("time_field："):
            time_field = line.split("：")[1].strip()
        elif line.startswith("dimension_field："):
            dimension_field = [x.strip() for x in line.split("：")[1].split(',')]
        elif line.startswith("metrics："):
            metrics = [x.strip() for x in line.split("：")[1].split(',')]
        elif line.startswith("filter_condition："):
            filter_condition_str = line.split("：")[1].strip()

    # 构造查询数组
    query_array = [time_field] + dimension_field + metrics

    # 解析 filter_condition 字符串为字典
    filter_dict = {}
    if filter_condition_str:
        # 使用正则表达式匹配键值对
        pattern = re.compile(r'(\w+)=([^=]+?)(?=,\s*\w+=|$)', re.UNICODE)
        matches = pattern.findall(filter_condition_str)
        for key, value_part in matches:
            key = key.strip()
            values = [v.strip() for v in value_part.split(',')]
            if key in {'年', '季', '月'}:
                # 排序并取最小最大值
                sorted_values = sorted(values)
                filter_dict[key] = [sorted_values[0], sorted_values[-1]]
            else:
                filter_dict[key] = values

    # 组合成最终字典
    result = {
        "query": query_array,
        "filters": filter_dict
    }

    return result

if __name__ == "__main__":
    main()

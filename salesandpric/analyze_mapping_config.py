import json
import os

def analyze_mapping_config():
    """
    分析mapping_config.json文件中mapping_config下一层级的属性值数量
    """
    config_file = 'mapping_config.json'
    
    # 检查文件是否存在
    if not os.path.exists(config_file):
        print(f"错误：找不到文件 {config_file}")
        return
    
    try:
        # 读取JSON文件
        with open(config_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 检查是否包含mapping_config键
        if 'id_mapping_config' not in data:
            print("错误：JSON文件中没有找到 'id_mapping_config' 键")
            return
        
        mapping_config = data['id_mapping_config']
        
        # 分析下一层级的属性
        print("=" * 50)
        print("id_mapping_config 下一层级属性分析")
        print("=" * 50)
        
        if isinstance(mapping_config, dict):
            total_properties = len(mapping_config.keys())
            print(f"总属性数量: {total_properties}")
            print("\n属性列表:")
            print("-" * 30)
            
            for i, key in enumerate(mapping_config.keys(), 1):
                value = mapping_config[key]
                value_type = type(value).__name__
                
                # 如果值是字典或列表，显示其长度
                if isinstance(value, dict):
                    length_info = f" (包含 {len(value)} 个子项)"
                elif isinstance(value, list):
                    length_info = f" (包含 {len(value)} 个元素)"
                else:
                    length_info = ""
                
                print(f"{i:2d}. {key:<25} [{value_type}]{length_info}")
            
            print("\n" + "=" * 50)
            print("详细统计:")
            print("-" * 30)
            
            # 统计不同数据类型的数量
            type_counts = {}
            for value in mapping_config.values():
                value_type = type(value).__name__
                type_counts[value_type] = type_counts.get(value_type, 0) + 1
            
            for data_type, count in type_counts.items():
                print(f"{data_type}: {count} 个")
            
            # 如果有嵌套的字典，显示更详细的信息
            print("\n" + "=" * 50)
            print("嵌套结构分析:")
            print("-" * 30)
            
            for key, value in mapping_config.items():
                if isinstance(value, dict) and len(value) > 0:
                    print(f"\n{key}:")
                    print(f"  包含 {len(value)} 个子属性")
                    # 显示前5个子属性作为示例
                    sample_keys = list(value.keys())[:5]
                    for sample_key in sample_keys:
                        print(f"    - {sample_key}")
                    if len(value) > 5:
                        print(f"    ... 还有 {len(value) - 5} 个")
                        
        else:
            print(f"id_mapping_config 不是字典类型，而是: {type(mapping_config).__name__}")
            
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
    except Exception as e:
        print(f"处理文件时出错: {e}")

if __name__ == "__main__":
    analyze_mapping_config()
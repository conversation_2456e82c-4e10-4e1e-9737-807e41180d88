import re
import time

import pandas as pd
from fuzzywuzzy import fuzz, process
import numpy as np
import jieba
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import question2Json as q2j
import requests
import json
import logging
from typing import Dict, Any, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('QueryMapper')


class EnhancedQueryMapper:
    def __init__(self, mapping_config=None, id_mapping_config=None, fuzzy_threshold=85, semantic_threshold=0.7):
        """
        初始化增强版映射处理器
        :param mapping_config: 映射配置字典
        :param id_mapping_config: ID映射配置字典
        :param fuzzy_threshold: 模糊匹配阈值(0-100)
        :param semantic_threshold: 语义匹���阈值(0-1)
        """
        self.mapping_config = mapping_config or {}
        self.id_mapping_config = id_mapping_config or {}  # 新增ID映射配置
        self.fuzzy_threshold = fuzzy_threshold
        self.semantic_threshold = semantic_threshold
        self.unmapped_values = set()

        # 预构建语义匹配模型
        self.semantic_models = {}
        self._build_semantic_models()

        logger.info("EnhancedQueryMapper initialized with fuzzy_threshold=%d, semantic_threshold=%.2f",
                    fuzzy_threshold, semantic_threshold)

    def _build_semantic_models(self):
        """为每个维度构建语义匹配模型"""
        for dimension, mapping_dict in self.mapping_config.items():
            # 获取所有可能的别名
            aliases = list(mapping_dict.keys())
            if not aliases:  # 如果没有别名，跳过这个维度
                continue

            # 使用jieba分词
            tokenized_aliases = [" ".join(jieba.cut(alias)) for alias in aliases]

            # 创建TF-IDF向量器，关闭停用词过滤，保留所有词汇
            vectorizer = TfidfVectorizer(
                stop_words=None,  # 不使用停用词
                token_pattern=r"(?u)\b\w+\b",  # 匹配任何词
                min_df=1,  # 最小文档频率为1，保留所有词
            )

            try:
                tfidf_matrix = vectorizer.fit_transform(tokenized_aliases)

                # 存储模型
                self.semantic_models[dimension] = {
                    'vectorizer': vectorizer,
                    'tfidf_matrix': tfidf_matrix,
                    'aliases': aliases,
                    'mapping_dict': mapping_dict
                }

                logger.debug("Built semantic model for dimension: %s with %d aliases", dimension, len(aliases))
            except ValueError as e:
                logger.warning("Failed to build semantic model for dimension %s: %s", dimension, str(e))
                continue  # 跳过这个维度，继续处理下一个

    def _semantic_match(self, dimension: str, value: str) -> str:
        """执行语义匹配"""
        if dimension not in self.semantic_models:
            return None

        model = self.semantic_models[dimension]

        # 分词处理输��值
        tokenized_value = " ".join(jieba.cut(value))

        # 转换为TF-IDF向量
        value_vector = model['vectorizer'].transform([tokenized_value])

        # 计算余弦相似度
        similarities = cosine_similarity(value_vector, model['tfidf_matrix'])
        max_index = np.argmax(similarities)
        max_similarity = similarities[0, max_index]

        # 检查������超过阈值
        if max_similarity >= self.semantic_threshold:
            best_alias = model['aliases'][max_index]
            standard_value = model['mapping_dict'][best_alias]
            logger.debug("Semantic match: '%s' (similarity=%.2f) -> '%s'",
                         value, max_similarity, standard_value)
            return standard_value

        return None

    def _fuzzy_match(self, dimension: str, value: str) -> str:
        """执行模糊匹配"""
        if dimension not in self.mapping_config:
            return None

        mapping_dict = self.mapping_config[dimension]
        aliases = list(mapping_dict.keys())

        # 使用fuzzywuzzy进行模糊匹配
        best_match, score = process.extractOne(value, aliases, scorer=fuzz.token_set_ratio)

        # 检查是否超过阈值
        if score >= self.fuzzy_threshold:
            standard_value = mapping_dict[best_match]
            logger.debug("Fuzzy match: '%s' (score=%d) -> '%s'",
                         value, score, standard_value)
            return standard_value

        return None

    def _exact_match(self, dimension: str, value: str) -> str:
        """执行精确匹配"""
        if dimension in self.mapping_config:
            mapping_dict = self.mapping_config[dimension]

            # 1. 精确匹配
            if value in mapping_dict:
                logger.debug("Exact match: '%s' -> '%s'", value, mapping_dict[value])
                return mapping_dict[value]

            # 2. 部分匹配（检查别名是否是原值的子串）
            for alias, standard in mapping_dict.items():
                if alias in value:
                    logger.debug("Partial match: '%s' (contains '%s') -> '%s'",
                                 value, alias, standard)
                    return standard

        return None

    def _value_to_id(self, dimension: str, value: str) -> str:
        """
        将标准值转换为对应的ID
        """
        if dimension in self.id_mapping_config:
            id_map = self.id_mapping_config[dimension]
            if value in id_map:
                logger.debug("ID mapping: '%s' -> '%s'", value, id_map[value])
                return id_map[value]
            # 处理多值情况（如"奔驰，宝马，奥迪"）
            if '，' in value:
                values = value.split('，')
                mapped_ids = []
                for v in values:
                    if v in id_map:
                        mapped_ids.append(id_map[v])
                if mapped_ids:
                    return ','.join(mapped_ids)
        return value

    def map_single_condition(self, condition: str) -> str:
        """
        映射单个条件表达式（key=value），使用三级匹配策略并转换为ID
        """
        if '=' not in condition:
            return condition

        key, value = condition.split('=', 1)
        key = key.strip()
        original_value = value.strip()

        logger.debug("Mapping condition: %s=%s", key, original_value)

        # 三级匹配策略
        matched_value = None

        # 1. 精确匹配（包含部分匹配）
        matched_value = self._exact_match(key, original_value)

        # 2. 模糊匹配
        if not matched_value:
            matched_value = self._fuzzy_match(key, original_value)

        # 3. 语义匹配
        if not matched_value and key in self.semantic_models:
            matched_value = self._semantic_match(key, original_value)

        # 应用匹配结果
        if matched_value:
            # 将匹配到的标准值转换为ID
            id_value = self._value_to_id(key, matched_value)
            self.unmapped_values.add(f"Matched: [{original_value}] -> [{matched_value}] -> ID[{id_value}]")
            return f"{key}={id_value}"
        else:
            self.unmapped_values.add(f"No match: [{original_value}] for key [{key}]")
            logger.warning("No match found for %s=%s", key, original_value)
            return condition

    def map_filter_condition(self, filter_str: str) -> str:
        """
        映射完整的filter_condition字符串
        """
        # 规范化输入：替换中文标点，去除多余空格
        normalized_condition = filter_str.replace("，", ",").replace("；", ",").strip()

        # 智能解析条件字符串，正确处理同一key对应多个值的情况
        grouped_conditions = {}
        
        # 改进的解析逻辑：先找到所有的key=开始位置
        import re
        
        # 找到所有key的位置
        key_pattern = r'(\w+(?:-\w+)*)\s*='
        key_matches = list(re.finditer(key_pattern, normalized_condition))
        
        # print("找到的key位置:")
        # for i, match in enumerate(key_matches):
        #     print(f"  {i}: {match.group(1)} at position {match.start()}")
        
        # 根据key的位置分割字符串
        for i, match in enumerate(key_matches):
            key = match.group(1).strip()
            start_pos = match.end()  # =号后面的位置
            
            # 确定值的结束位置
            if i + 1 < len(key_matches):
                # 不是最后一个key，值到下一个key之前
                end_pos = key_matches[i + 1].start()
                value_string = normalized_condition[start_pos:end_pos].rstrip(', ')
            else:
                # 最后一个key，值到字符串结束
                value_string = normalized_condition[start_pos:].rstrip(', ')
            
            # 分割多个值
            values = [v.strip() for v in value_string.split(',') if v.strip()]
            grouped_conditions[key] = values
            # print(f"解析到 {key}: {values}")

        # 处理每个分组的条件
        mapped_conditions = []
        for key, values in grouped_conditions.items():
            # 对每个值单独进行映射
            mapped_values = []
            for v in values:
                if v:  # 确保值不为空
                    single_cond = f"{key}={v}"
                    mapped = self.map_single_condition(single_cond)
                    # 提取映射后的值部分
                    if '=' in mapped:
                        mapped_value = mapped.split('=', 1)[1]
                        mapped_values.append(mapped_value)
                    else:
                        mapped_values.append(v)  # 如果映射失败，保持原值
            
            # 添加映射后的条件（多个值用逗号连接）
            if mapped_values:
                mapped_conditions.append(f"{key}={','.join(mapped_values)}")

        return ','.join(mapped_conditions)

    def get_unmapped_log(self) -> list:
        """获取未成功映射的值日志"""
        return list(self.unmapped_values)

    def reset_log(self):
        """重置未映射值日志"""
        self.unmapped_values = set()


def process_llm_output(llm_output: dict, mapper: EnhancedQueryMapper) -> dict:
    """
    处理LLM输出，专门映射filter_condition字段
    """
    # 深拷贝原始输出以避免修改原始数据
    processed_output = llm_output.copy()

    # 检查并处理filter_condition字段
    if "filter_condition" in processed_output:
        filter_condition = processed_output["filter_condition"]

        # 应用映射处理
        mapped_condition = mapper.map_filter_condition(filter_condition)
        processed_output["filter_condition"] = mapped_condition

    return processed_output

def format_query_filter(input_string):
    # 构造查询数组
    query_array = []
    query_array.append(input_string['time_field'])
    query_array.append(input_string['dimension_field'])
    # 将metrics字段按逗号分割为数组，并去除每个元素的首尾空格
    for m in input_string['metrics'].split(','):
        query_array.append(m.strip())

    filter_condition_str = input_string['filter_condition']

    # 解析 filter_condition 字符串为字典
    filter_dict = {}
    if filter_condition_str:
        # 使用正则表达式匹配键值对
        pattern = re.compile(r'(\w+)=([^=]+?)(?=,\s*\w+=|$)', re.UNICODE)
        matches = pattern.findall(filter_condition_str)
        for key, value_part in matches:
            key = key.strip()
            values = [v.strip() for v in value_part.split(',')]
            if key in {'年', '季', '月'}:
                # 排序并取最小最大值
                sorted_values = sorted(values)
                filter_dict[key] = [sorted_values[0], sorted_values[-1]]
            else:
                filter_dict[key] = values

    # 组合成最终字典
    result = {
        "query": query_array,
        "filters": filter_dict
    }

    return result


def query_data(query_json: Dict[str, Any]) -> Optional[Dict]:
    # 接口URL
    url = "http://***********:8099/multi/query-data"
    # url = "http://n.ways.cn/api/v3/iw-multi-data/multi/query-data?t=1755497190144&language=zh_CN"

    # 设置请求头
    headers = {
        'Content-Type': 'application/json'
    }

    try:
        # 发送POST请求
        response = requests.post(
            url=url,
            headers=headers,
            json=query_json,
            timeout=30  # 设置30秒超时
        )

        # 检查响应状态码
        response.raise_for_status()

        # 解析响应JSON数据
        result = response.json()

        return result

    except requests.exceptions.RequestException as e:
        logging.error(f"请求失败: {str(e)}")
        return None
    except json.JSONDecodeError as e:
        logging.error(f"JSON解析失败: {str(e)}")
        return None
    except Exception as e:
        logging.error(f"发生未知错误: {str(e)}")
        return None

# 从JSON文件加载配置
def load_config(file_path: str):
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def parse_model_output(text):
    """
    解析模型输出文本，转换为指定的JSON格式
    """
    # 初始化结果字典
    result = {}

    # 分行处理文本
    lines = text.strip().split('\n')

    # 解析每一行
    for line in lines:
        if '：' in line:
            key, value = line.split('：', 1)  # 使用中文冒号分割
            key = key.strip()
            value = value.strip()

            # 映射字段名
            if key == 'time_field':
                result['time_field'] = value
            elif key == 'dimension_field':
                result['dimension_field'] = value
            elif key == 'metrics':
                result['metrics'] = value
            elif key == 'filter_condition':
                result['filter_condition'] = value

    return result

# ==================== 使用示例 ====================
if __name__ == "__main__":
    # 配置日志级别
    logging.basicConfig(level=logging.INFO)

    # 步骤1: 配置映射规则
    config_data = load_config('mapping_config.json')
    config = config_data['mapping_config']
    # ID映射配置示例
    id_config = config_data['id_mapping_config']

    # 步骤2: 初始化增强版处理器
    mapper = EnhancedQueryMapper(
        mapping_config=config,
        id_mapping_config=id_config,  # 添加ID映射配置
        fuzzy_threshold=80,  # 模糊匹配阈值
        semantic_threshold=0.65  # 语义匹配阈值
    )


    # 读取Excel文件
    df = pd.read_excel(r'C:\Users\<USER>\Desktop\用户问题-测试100.xlsx')
    
    # 添加新列（如果不存在），明确指定数据类型为字符串类型
    if '原始LLM输出' not in df.columns:
        df['原始LLM输出'] = pd.Series(dtype='object')
    if '处理后的输出' not in df.columns:
        df['处理后的输出'] = pd.Series(dtype='object')
    if '执行时间' not in df.columns:
        df['执行时间'] = pd.Series(dtype='object')
    
    # 遍历每个问题
    for index, row in df.iterrows():
        # 程序执行开始时间
        start_time = time.time()
        print("开始处理第", index + 1, "个问题")
        question = row['问题']
        # 如果原始LLM输出列已有值，则跳过处理
        # print("原始LLM输出:",df.at[index, '原始LLM输出'])
        if (df.at[index, '原始LLM输出'] != '' and pd.notna(df.at[index, '原始LLM输出'])) or (df.at[index, '处理后的输出'] is not None and pd.notna(df.at[index, '处理后的输出'])):
            print(f"第{index + 1}个问题已有原始LLM输出，跳过处理")
            continue

        analyze_question = q2j.analyze_question(question)

        llm_output = parse_model_output(analyze_question)

        # 检查API返回的数据是否完整
        if not all(key in llm_output for key in ['time_field', 'dimension_field', 'metrics', 'filter_condition']):
            print("API返回数据不完整，请检查API连接和返回结果")
            df.at[index, '原始LLM输出'] = str("API调用失败")
            continue
        
        # 将结果写回Excel（转换为字符串格式）
        df.at[index, '原始LLM输出'] = str(json.dumps(llm_output, ensure_ascii=False, indent=2))
        print("原始LLM输出:")
        print(f"time_field: {llm_output['time_field']}")
        print(f"dimension_field: {llm_output['dimension_field']}")
        print(f"metrics: {llm_output['metrics']}")
        print(f"filter_condition: {llm_output['filter_condition']}")

        # 处理输出
        processed_output = process_llm_output(llm_output, mapper)

        df.at[index, '处理后的输出'] = str(json.dumps(processed_output, ensure_ascii=False, indent=2))

        print("\n处理后的输出:")
        print(f"time_field: {processed_output['time_field']}")
        print(f"dimension_field: {processed_output['dimension_field']}")
        print(f"metrics: {processed_output['metrics']}")
        print(f"filter_condition: {processed_output['filter_condition']}")

        end_time = time.time()
        exe_time = str(round(end_time - start_time, 2)) + "秒"
        df.at[index, '执行时间'] = str(exe_time)

        # 保存Excel
        # 每10个问题保存一次
        if (index + 1) % 10 == 0:
            df.to_excel(r'C:\Users\<USER>\Desktop\用户问题-测试100.xlsx', index=False)
            print(f"已保存第{index + 1}个问题的结果")

    df.to_excel(r'C:\Users\<USER>\Desktop\用户问题-测试100.xlsx', index=False)

    # 解析JSON并调用多维接口
    # query_filter = format_query_filter(processed_output)
    # # print("query_filter:",query_filter)
    #
    # last_json_result = q2j.query_to_json(query_filter)
    # print(json.dumps(last_json_result, indent=2, ensure_ascii=False))
    #
    # data_result = query_data(last_json_result)
    #
    # # print("data_result:",data_result)
    #
    # # 添加数据检查
    # if data_result is None or data_result.get('data') is None:
    #     print("未获取到有效数据")
    #     exit()
    #
    # # 提取columns和data
    # columns = data_result['data']['columns']
    # data = data_result['data']['data']
    #
    # # 提取列标题
    # titles = [col['title'] for col in columns]
    #
    # # 生成Markdown表格
    # markdown_table = "| " + " | ".join(titles) + " |\n"
    # markdown_table += "| " + " | ".join(["---"] * len(titles)) + " |\n"
    #
    # # 生成数据行
    # for row in data:
    #     row_data = []
    #     for col in columns:
    #         data_index = col['dataIndex']
    #         value = row.get(data_index, '')
    #         row_data.append(str(value))
    #
    #     markdown_table += "| " + " | ".join(row_data) + " |\n"
    #
    # print(markdown_table)
    # end_time = time.time()
    # print(f"程序执行时间: {round(end_time - start_time, 2)}秒")
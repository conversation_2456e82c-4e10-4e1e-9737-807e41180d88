import pandas as pd
from openai import OpenAI
from typing import List, Dict
import time
from tqdm import tqdm
import json

class ModelAliasGenerator:
    def __init__(self, excel_path: str, api_key: str, api_base_url: str):
        self.excel_path = excel_path
        self.batch_size = 500  # Reduce batch size to improve model response completeness
        
        # Configure API settings
        self.client = OpenAI(
            api_key=api_key,
            base_url=api_base_url
        )
        self.model = "Qwen/Qwen3-235B-A22B-Instruct-2507"

    def read_excel_data(self) -> pd.DataFrame:
        """Read the model list from Excel file"""
        try:
            df = pd.read_excel(self.excel_path)
            # Ensure required columns exist
            required_columns = ['型号ID', '型号简称', '型号名称']
            for col in required_columns:
                if col not in df.columns:
                    raise ValueError(f"Required column '{col}' not found in Excel file")
            return df
        except Exception as e:
            print(f"Error reading Excel file: {e}")
            raise

    def generate_aliases(self, model_data: List[Dict]) -> List[Dict]:
        """Generate aliases for a batch of model names using DeepSeek-V3"""
        formatted_models = [f"ID: {item['id']}, 简称: {item['short_name']}, 名称: {item['full_name']}" for item in model_data]
        
        prompt = f"""
请为以下汽车型号生成 3-5个 常用且简洁的中文别名。
【别名生成规则 (必须严格遵守)】
保留核心信息：每个别名必须同时包含“车系”和“版本等级”（如：豪华型、标准型、尊贵版）这两个核心要素。
品牌可选：其中 1-2个 别名应保留品牌名（如：奇瑞），其余别名可省略品牌以更简洁。
年份处理：年份需简化为“XX款”格式（如：2003→03款）。可选择性地在部分别名中使用。
配置简化：排量和变速箱信息可简化为“X.X手动”或“X.X自动”格式（如：1.1L手动→1.1手动）。可选择性地在部分别名中使用。
禁止信息缺失：严禁生成缺少“版本等级”或“车系”的别名。严禁创造无意义的数字型号缩写（如：QQ31.1）。
别名不重复：对于同一个车系下的多个型号，别名不要重复。

【生成示例】
输入：奇瑞 QQ3 2003 1.1L 手动 豪华型
期望输出：["奇瑞QQ3豪华型", "03款QQ3豪华版", "QQ3 1.1手动豪华型"]
错误输出：["奇瑞QQ3", "03款QQ3", "QQ31.1"] （原因：缺少“豪华型”版本信息，或信息拼接错误）

型号列表：
{chr(10).join(formatted_models)}

请直接返回JSON数组，格式：[{{"id": "1", "aliases": ["别名1", "别名2", "别名3"]}}]"""

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=4000  # Increase max tokens for larger responses
            )
            
            # Extract and parse the response
            result = response.choices[0].message.content
            try:
                # Extract JSON from the response (it might be wrapped in markdown)
                json_start = result.find('[')
                json_end = result.rfind(']') + 1
                
                if json_start != -1 and json_end != -1:
                    json_str = result[json_start:json_end]
                    parsed_result = json.loads(json_str)
                    print(f"Successfully extracted and parsed JSON with {len(parsed_result)} items")
                    
                    # Verify that we have the expected number of results
                    expected_count = len(model_data)
                    actual_count = len(parsed_result)
                    if actual_count != expected_count:
                        print(f"WARNING: Expected {expected_count} results but got {actual_count}")
                        missing_ids = set(str(item['id']) for item in model_data) - set(str(item['id']) for item in parsed_result)
                        print(f"Missing {len(missing_ids)} IDs")
                        
                        # Fill in missing IDs with practical fallback aliases
                        for missing_id in missing_ids:
                            # Find the original model data for this ID
                            missing_model = next((item for item in model_data if str(item['id']) == missing_id), None)
                            if missing_model:
                                # Extract useful components from the full name
                                full_name = missing_model['full_name'] or ""
                                short_name = missing_model['short_name'] or ""
                                
                                # Try to extract brand, model, year, and config
                                parts = full_name.split()
                                brand = parts[0] if len(parts) > 0 else "未知品牌"
                                model = parts[1] if len(parts) > 1 else short_name
                                year = ""
                                config = ""
                                
                                # Look for year (4 digits)
                                for part in parts:
                                    if part.isdigit() and len(part) == 4:
                                        year = part[-2:] + "年"  # Convert 2005 to 05年
                                        break
                                
                                # Look for displacement and transmission
                                for part in parts:
                                    if "L" in part and any(c.isdigit() for c in part):
                                        config = part.replace("L", "")
                                        break
                                
                                # Generate practical aliases
                                fallback_aliases = [
                                    f"{brand}{model}" if brand != "未知品牌" else short_name,
                                    f"{year}{model}" if year else f"{model}款",
                                    f"{model}{config}" if config else f"{short_name}型"
                                ]
                                
                                # Remove duplicates and empty strings
                                fallback_aliases = list(dict.fromkeys([alias for alias in fallback_aliases if alias]))
                                
                                # Ensure we have 3 aliases
                                while len(fallback_aliases) < 3:
                                    fallback_aliases.append(f"{short_name}_{len(fallback_aliases)+1}")
                                
                                parsed_result.append({
                                    "id": missing_id,
                                    "aliases": fallback_aliases[:3]
                                })
                        
                        print(f"Added fallback aliases for {len(missing_ids)} missing IDs")
                    
                    return parsed_result
                else:
                    # Try to parse the whole response as JSON
                    parsed_result = json.loads(result)
                    return parsed_result
            except json.JSONDecodeError as e:
                print(f"Error parsing JSON response: {e}")
                print(f"Raw response: {result[:500]}...")  # Only show first 500 chars
                
                # Try to extract JSON manually using regex
                import re
                json_pattern = r'\[\s*\{.*?\}\s*\]'
                json_matches = re.findall(json_pattern, result, re.DOTALL)
                
                if json_matches:
                    try:
                        parsed_result = json.loads(json_matches[0])
                        print(f"Successfully extracted JSON using regex with {len(parsed_result)} items")
                        return parsed_result
                    except:
                        pass
                
                return [{"id": item['id'], "aliases": []} for item in model_data]
        except Exception as e:
            print(f"Error generating aliases: {e}")
            print("Generating fallback aliases...")
            # Generate practical fallback aliases when API fails
            fallback_results = []
            for item in model_data:
                # Create practical fallback aliases based on the model name
                short_name = item['short_name'] or ""
                full_name = item['full_name'] or ""
                
                # Extract useful components
                parts = full_name.split() if full_name else [short_name]
                brand = parts[0] if len(parts) > 0 else "未知"
                model = parts[1] if len(parts) > 1 else short_name
                
                # Look for year and config
                year = ""
                config = ""
                for part in parts:
                    if part.isdigit() and len(part) == 4:
                        year = part[-2:] + "年"
                    elif "L" in part and any(c.isdigit() for c in part):
                        config = part.replace("L", "")
                
                # Generate practical aliases
                fallback_aliases = []
                if brand and model:
                    fallback_aliases.append(f"{brand}{model}")
                if year and model:
                    fallback_aliases.append(f"{year}{model}")
                if config and model:
                    fallback_aliases.append(f"{model}{config}")
                if short_name:
                    fallback_aliases.append(f"{short_name}款")
                
                # Remove duplicates and ensure uniqueness
                fallback_aliases = list(dict.fromkeys([alias for alias in fallback_aliases if alias]))
                
                # Ensure we have at least 3 aliases
                while len(fallback_aliases) < 3:
                    fallback_aliases.append(f"型号{item['id']}_{len(fallback_aliases)+1}")
                
                fallback_results.append({
                    "id": item['id'],
                    "aliases": fallback_aliases[:3]  # Limit to 3 aliases
                })
            
            print(f"Generated {len(fallback_results)} fallback results")
            return fallback_results

    def process_in_batches(self):
        """Process the model list in batches of 500"""
        # Read the Excel file
        df = self.read_excel_data()
        
        # Add new columns for aliases if they don't exist
        for i in range(5):
            if f'alias_{i+1}' not in df.columns:
                df[f'alias_{i+1}'] = None

        # Process in batches
        for i in tqdm(range(0, len(df), self.batch_size)):
            batch = df.iloc[i:i + self.batch_size]
            print(f"\n=== Processing batch {i//self.batch_size + 1} ===")
            print(f"Batch size: {len(batch)} rows (from index {i} to {i + len(batch) - 1})")
            
            # Prepare model data for the batch
            model_data = [
                {
                    'id': str(row['型号ID']),
                    'short_name': row['型号简称'],
                    'full_name': row['型号名称']
                }
                for _, row in batch.iterrows()
            ]
            print(f"Prepared {len(model_data)} model entries for API")
            
            # Generate aliases for the batch
            results = self.generate_aliases(model_data)
            print(f"API returned {len(results)} results")
            
            # Update the DataFrame with aliases
            print(f"Processing {len(results)} results from API...")
            successfully_updated = 0
            failed_matches = 0
            
            for result in results:
                model_id = str(result['id'])
                aliases = result['aliases']
                print(f"Model ID {model_id}: {len(aliases)} aliases - {aliases}")
                
                # Find the index of the model ID in the DataFrame
                try:
                    matching_rows = df[df['型号ID'].astype(str) == model_id]
                    if matching_rows.empty:
                        print(f"  Warning: Model ID {model_id} not found in DataFrame")
                        failed_matches += 1
                        continue
                    idx = matching_rows.index[0]
                    
                    # Update aliases columns
                    aliases_stored = 0
                    for j, alias in enumerate(aliases[:5]):  # Only take up to 5 aliases
                        if alias:  # Only store non-empty aliases
                            df.at[idx, f'alias_{j+1}'] = alias
                            print(f"  Stored alias_{j+1}: {alias}")
                            aliases_stored += 1
                    
                    if aliases_stored > 0:
                        successfully_updated += 1
                        
                except Exception as e:
                    print(f"  Error updating model ID {model_id}: {e}")
                    failed_matches += 1
                    continue
            
            print(f"Batch summary: {successfully_updated} models updated, {failed_matches} failed matches")
            
            # Save after each batch
            output_path = self.excel_path.replace('.xlsx', '_with_aliases.xlsx')
            df.to_excel(output_path, index=False, engine='openpyxl')
            print(f"Saved batch results to {output_path}")
            
            # Verify that aliases were actually stored
            alias_columns = [f'alias_{i+1}' for i in range(5)]
            non_empty_aliases = df[alias_columns].notna().sum().sum()
            print(f"Total non-empty aliases stored: {non_empty_aliases}")
            
            # Add a small delay to avoid API rate limits
            time.sleep(1)

        print("Processing completed. Results saved to Excel file.")
        
        # Final verification
        final_df = pd.read_excel(output_path)
        alias_columns = [f'alias_{i+1}' for i in range(5)]
        total_aliases = final_df[alias_columns].notna().sum().sum()
        print(f"Final verification: {total_aliases} aliases stored in {output_path}")

def test_api_connection():
    """Test API connection with a simple request"""
    api_key = "sk-vRFFqMG3ba5FpvxXJ689ZWUP78SKn5UYlsz8ifVF47wo86Nf"
    api_base_url = "https://yunwu.ai/v1"
    
    client = OpenAI(
        api_key=api_key,
        base_url=api_base_url
    )
    
    try:
        response = client.chat.completions.create(
            model="gemini-2.5-pro",
            messages=[{"role": "user", "content": "Hello, please respond with 'API working'"}],
            max_tokens=10
        )
        print(f"API Test Success: {response.choices[0].message.content}")
        return True
    except Exception as e:
        print(f"API Test Failed: {e}")
        # Try alternative model names
        try:
            response = client.chat.completions.create(
                model="gemini-2.5-pro",
                messages=[{"role": "user", "content": "Hello, please respond with 'API working'"}],
                max_tokens=10
            )
            print(f"API Test Success with alternative model: {response.choices[0].message.content}")
            return True
        except Exception as e2:
            print(f"Alternative model also failed: {e2}")
            return False

def main():
    # Configuration
    excel_path = r"C:\Users\<USER>\Desktop\型号清单.xlsx"
    api_key = "sk-vRFFqMG3ba5FpvxXJ689ZWUP78SKn5UYlsz8ifVF47wo86Nf"
    # api_key = "sk-awbxpcoxjgcvswaupencngodsxuhbbuswjocamzcmgbkkelw" # 硅基流动KEY
    # api_base_url = "https://api.siliconflow.cn/v1"
    api_base_url = "https://yunwu.ai/v1"

    # Test API connection first
    print("Testing API connection...")
    if not test_api_connection():
        print("API connection failed. Please check your API key and base URL.")
        return
    
    # Create and run the generator
    generator = ModelAliasGenerator(excel_path, api_key, api_base_url)
    generator.process_in_batches()

if __name__ == "__main__":
    main()
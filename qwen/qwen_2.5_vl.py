import requests
import json
import base64
import pandas as pd

def encode_image_from_path(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

# API 配置
api_key = "sk-7b9067ef70794d7286da8432be28f18c"
base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"

# 准备图片数据
image_path = r"C:\Users\<USER>\Desktop\250729_i8参数配置中文(2)_9.png"
image_base64 = encode_image_from_path(image_path)

# 准备请求数据
headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json"
}

payload = {
    "model": "qwen2.5-vl-72b-instruct",
    "messages": [
        {
            "role": "user",
            "content": [
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{image_base64}"
                    }
                },
                {
                    "type": "text",
                    "text": "请完整提取并这张图片中的所有的表格内容"
                }
            ]
        }
    ]
}

# 发送请求
response = requests.post(base_url, headers=headers, json=payload)
extracted_content = response.json()['choices'][0]['message']['content']
print(extracted_content)


